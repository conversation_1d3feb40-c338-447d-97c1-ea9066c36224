import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import {BasicDetails} from '../../../src/pages/CreateRA/BasicDetails';
import {TemplateForm} from '../../../src/types/template';

// Mock InputComponent
jest.mock('../../../src/components/InputComponent', () => ({
  InputComponent: ({
    label,
    name,
    value,
    onChange,
    onBlur,
    error,
    placeholder,
    type,
    maxLength,
    rows,
    form,
  }: any) => (
    <div data-testid={`input-${name}`}>
      <label htmlFor={name}>{label}</label>
      {type === 'textarea' ? (
        <textarea
          id={name}
          name={name}
          value={form?.[name] || value || ''}
          onChange={onChange}
          onBlur={onBlur}
          placeholder={placeholder}
          maxLength={maxLength}
          rows={rows}
        />
      ) : (
        <input
          id={name}
          name={name}
          value={form?.[name] || value || ''}
          onChange={onChange}
          onBlur={onBlur}
          placeholder={placeholder}
          maxLength={maxLength}
          type={type || 'text'}
        />
      )}
      <div data-testid={`error-${name}`}>{error || ''}</div>
    </div>
  ),
}));

describe('BasicDetails Component', () => {
  const mockSetForm = jest.fn();
  const mockOnValidate = jest.fn();

  const defaultForm: TemplateForm = {
    task_requiring_ra: '',
    task_duration: '',
    task_duration_unit: null,
    task_alternative_consideration: '',
    task_rejection_reason: '',
    worst_case_scenario: '',
    recovery_measures: '',
    status: '',
    parameters: [],
    risk_template_category: {
      risk_template_category_id: [],
      is_other: false,
      value: '',
    },
    risk_template_hazard: {
      risk_template_hazard_id: [],
      is_other: false,
      value: '',
    },
    risk_template_job: [],
    risk_template_task_reliability_assessment: [],
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  const renderBasicDetails = (
    form = defaultForm,
    onValidate = mockOnValidate,
  ) => {
    const ref = React.createRef<{validate: () => boolean}>();
    const result = render(
      <BasicDetails
        ref={ref}
        form={form}
        setForm={mockSetForm}
        onValidate={onValidate}
      />,
    );
    return {...result, ref};
  };

  describe('Rendering', () => {
    it('renders the component with all required fields', () => {
      renderBasicDetails();

      expect(screen.getByText('Enter Basic RA Details')).toBeInTheDocument();
      expect(screen.getByLabelText('Task Requiring R.A.')).toBeInTheDocument();
      expect(screen.getByLabelText('Duration of Task')).toBeInTheDocument();
      expect(
        screen.getByLabelText('Alternative Considered to carry out above task'),
      ).toBeInTheDocument();
      expect(screen.getByLabelText('Reason for Rejection')).toBeInTheDocument();
    });

    it('renders input fields with correct attributes', () => {
      renderBasicDetails();

      const taskRequiringInput = screen.getByLabelText('Task Requiring R.A.');
      expect(taskRequiringInput).toHaveAttribute('name', 'task_requiring_ra');
      expect(taskRequiringInput).toHaveAttribute(
        'placeholder',
        'Enter the task requiring risk assessment',
      );
      expect(taskRequiringInput).toHaveAttribute('maxLength', '255');

      const durationInput = screen.getByLabelText('Duration of Task');
      expect(durationInput).toHaveAttribute('name', 'task_duration');
      expect(durationInput).toHaveAttribute(
        'placeholder',
        'Enter No. of Days/Hours Required',
      );

      const alternativeInput = screen.getByLabelText(
        'Alternative Considered to carry out above task',
      );
      expect(alternativeInput).toHaveAttribute(
        'name',
        'task_alternative_consideration',
      );

      const rejectionInput = screen.getByLabelText('Reason for Rejection');
      expect(rejectionInput).toHaveAttribute('name', 'task_rejection_reason');
      expect(rejectionInput).toHaveAttribute('maxLength', '4000');
    });

    it('renders textarea for reason for rejection field', () => {
      renderBasicDetails();

      const rejectionInput = screen.getByLabelText('Reason for Rejection');
      expect(rejectionInput.tagName).toBe('TEXTAREA');
      expect(rejectionInput).toHaveAttribute('rows', '3');
    });

    it('displays form values correctly', () => {
      const formWithValues = {
        ...defaultForm,
        task_requiring_ra: 'Test task',
        task_duration: '5',
        task_alternative_consideration: 'Test alternative',
        task_rejection_reason: 'Test rejection reason',
      };

      renderBasicDetails(formWithValues);

      expect(screen.getByDisplayValue('Test task')).toBeInTheDocument();
      expect(screen.getByDisplayValue('5')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test alternative')).toBeInTheDocument();
      expect(
        screen.getByDisplayValue('Test rejection reason'),
      ).toBeInTheDocument();
    });
  });

  describe('Form Interactions', () => {
    it('calls setForm when input values change', async () => {
      const user = userEvent.setup();
      renderBasicDetails();

      const taskInput = screen.getByLabelText('Task Requiring R.A.');
      await user.type(taskInput, 'New task');

      expect(mockSetForm).toHaveBeenCalled();
    });

    it('handles onChange events correctly', () => {
      renderBasicDetails();

      const taskInput = screen.getByLabelText('Task Requiring R.A.');
      fireEvent.change(taskInput, {
        target: {name: 'task_requiring_ra', value: 'Test task'},
      });

      expect(mockSetForm).toHaveBeenCalledWith({
        ...defaultForm,
        task_requiring_ra: 'Test task',
      });
    });

    it('handles onBlur events correctly', () => {
      renderBasicDetails();

      const taskInput = screen.getByLabelText('Task Requiring R.A.');
      fireEvent.blur(taskInput, {target: {name: 'task_requiring_ra'}});

      // Should trigger validation
      expect(mockOnValidate).toHaveBeenCalled();
    });

    it('updates touched state on blur', () => {
      renderBasicDetails();

      const taskInput = screen.getByLabelText('Task Requiring R.A.');
      fireEvent.blur(taskInput, {target: {name: 'task_requiring_ra'}});

      // After blur, error should be visible for empty field
      expect(screen.getByTestId('error-task_requiring_ra')).toHaveTextContent(
        'This is a mandatory field. Please fill to process.',
      );
    });

    it('clears validation error when field is filled', () => {
      renderBasicDetails();

      const taskInput = screen.getByLabelText('Task Requiring R.A.');

      // First trigger error by blurring empty field
      fireEvent.blur(taskInput, {target: {name: 'task_requiring_ra'}});
      expect(screen.getByTestId('error-task_requiring_ra')).toHaveTextContent(
        'This is a mandatory field. Please fill to process.',
      );

      // Then fill the field
      fireEvent.change(taskInput, {
        target: {name: 'task_requiring_ra', value: 'Test task'},
      });

      // Error should be cleared
      expect(screen.getByTestId('error-task_requiring_ra')).toHaveTextContent(
        '',
      );
    });

    it('handles duration field as string conversion', () => {
      renderBasicDetails();

      const durationInput = screen.getByLabelText('Duration of Task');
      fireEvent.change(durationInput, {
        target: {name: 'task_duration', value: '5'},
      });

      expect(mockSetForm).toHaveBeenCalledWith({
        ...defaultForm,
        task_duration: '5',
      });
    });
  });

  describe('Validation', () => {
    it('validates all required fields are empty initially', () => {
      const {ref} = renderBasicDetails();

      const isValid = ref.current?.validate();
      expect(isValid).toBe(false);
      expect(mockOnValidate).toHaveBeenCalledWith(false);
    });

    it('validates successfully when all fields are filled', () => {
      const formWithAllFields = {
        ...defaultForm,
        task_requiring_ra: 'Test task',
        task_duration: '5',
        task_alternative_consideration: 'Test alternative',
        task_rejection_reason: 'Test rejection',
      };

      const {ref} = renderBasicDetails(formWithAllFields);

      const isValid = ref.current?.validate();
      expect(isValid).toBe(true);
      expect(mockOnValidate).toHaveBeenCalledWith(true);
    });

    it('fails validation when task_requiring_ra is empty', () => {
      const formWithMissingTask = {
        ...defaultForm,
        task_requiring_ra: '',
        task_duration: '5',
        task_alternative_consideration: 'Test alternative',
        task_rejection_reason: 'Test rejection',
      };

      const {ref} = renderBasicDetails(formWithMissingTask);

      const isValid = ref.current?.validate();
      expect(isValid).toBe(false);
      expect(mockOnValidate).toHaveBeenCalledWith(false);
    });

    it('fails validation when task_duration is empty', () => {
      const formWithMissingDuration = {
        ...defaultForm,
        task_requiring_ra: 'Test task',
        task_duration: '',
        task_alternative_consideration: 'Test alternative',
        task_rejection_reason: 'Test rejection',
      };

      const {ref} = renderBasicDetails(formWithMissingDuration);

      const isValid = ref.current?.validate();
      expect(isValid).toBe(false);
      expect(mockOnValidate).toHaveBeenCalledWith(false);
    });

    it('fails validation when task_alternative_consideration is empty', () => {
      const formWithMissingAlternative = {
        ...defaultForm,
        task_requiring_ra: 'Test task',
        task_duration: '5',
        task_alternative_consideration: '',
        task_rejection_reason: 'Test rejection',
      };

      const {ref} = renderBasicDetails(formWithMissingAlternative);

      const isValid = ref.current?.validate();
      expect(isValid).toBe(false);
      expect(mockOnValidate).toHaveBeenCalledWith(false);
    });

    it('fails validation when task_rejection_reason is empty', () => {
      const formWithMissingRejection = {
        ...defaultForm,
        task_requiring_ra: 'Test task',
        task_duration: '5',
        task_alternative_consideration: 'Test alternative',
        task_rejection_reason: '',
      };

      const {ref} = renderBasicDetails(formWithMissingRejection);

      const isValid = ref.current?.validate();
      expect(isValid).toBe(false);
      expect(mockOnValidate).toHaveBeenCalledWith(false);
    });

    it('handles whitespace-only values as invalid', () => {
      const formWithWhitespace = {
        ...defaultForm,
        task_requiring_ra: '   ',
        task_duration: '5',
        task_alternative_consideration: 'Test alternative',
        task_rejection_reason: 'Test rejection',
      };

      const {ref} = renderBasicDetails(formWithWhitespace);

      const isValid = ref.current?.validate();
      expect(isValid).toBe(false);
      expect(mockOnValidate).toHaveBeenCalledWith(false);
    });

    it('validates with form containing all required fields', () => {
      const customForm = {
        ...defaultForm,
        task_requiring_ra: 'Custom task',
        task_duration: '3',
        task_alternative_consideration: 'Custom alternative',
        task_rejection_reason: 'Custom rejection',
      };

      const {ref} = renderBasicDetails(customForm);

      const isValid = ref.current?.validate();
      expect(isValid).toBe(true);
    });

    it('handles null task_duration correctly', () => {
      const formWithNullDuration = {
        ...defaultForm,
        task_requiring_ra: 'Test task',
        task_duration: null,
        task_alternative_consideration: 'Test alternative',
        task_rejection_reason: 'Test rejection',
      };

      const {ref} = renderBasicDetails(formWithNullDuration);

      const isValid = ref.current?.validate();
      expect(isValid).toBe(false);
      expect(mockOnValidate).toHaveBeenCalledWith(false);
    });
  });

  describe('Error Display', () => {
    it('shows validation errors only after field is touched', () => {
      renderBasicDetails();

      // Initially no errors should be visible
      expect(screen.getByTestId('error-task_requiring_ra')).toHaveTextContent(
        '',
      );

      // After blur, error should appear
      const taskInput = screen.getByLabelText('Task Requiring R.A.');
      fireEvent.blur(taskInput, {target: {name: 'task_requiring_ra'}});

      expect(screen.getByTestId('error-task_requiring_ra')).toHaveTextContent(
        'This is a mandatory field. Please fill to process.',
      );
    });

    it('displays correct error message for all fields', () => {
      renderBasicDetails();

      const fields = [
        'task_requiring_ra',
        'task_duration',
        'task_alternative_consideration',
        'task_rejection_reason',
      ];

      fields.forEach(fieldName => {
        const input = screen.getByLabelText(
          fieldName === 'task_requiring_ra'
            ? 'Task Requiring R.A.'
            : fieldName === 'task_duration'
            ? 'Duration of Task'
            : fieldName === 'task_alternative_consideration'
            ? 'Alternative Considered to carry out above task'
            : 'Reason for Rejection',
        );

        fireEvent.blur(input, {target: {name: fieldName}});

        expect(screen.getByTestId(`error-${fieldName}`)).toHaveTextContent(
          'This is a mandatory field. Please fill to process.',
        );
      });
    });
  });

  describe('Component Props', () => {
    it('works without onValidate callback', () => {
      const {ref} = renderBasicDetails(defaultForm, undefined);

      expect(() => {
        ref.current?.validate();
      }).not.toThrow();
    });

    it('exposes validate method through ref', () => {
      const {ref} = renderBasicDetails();

      expect(ref.current).toHaveProperty('validate');
      expect(typeof ref.current?.validate).toBe('function');
    });

    it('handles form updates correctly', () => {
      const updatedForm = {
        ...defaultForm,
        task_requiring_ra: 'Updated task',
      };

      const {rerender} = render(
        <BasicDetails
          form={defaultForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
        />,
      );

      expect(screen.getByLabelText('Task Requiring R.A.')).toHaveValue('');

      rerender(
        <BasicDetails
          form={updatedForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
        />,
      );

      expect(screen.getByLabelText('Task Requiring R.A.')).toHaveValue(
        'Updated task',
      );
    });
  });

  describe('Field-specific Validation', () => {
    it('validates individual fields correctly', () => {
      renderBasicDetails();

      // Test task_requiring_ra validation
      const taskInput = screen.getByLabelText('Task Requiring R.A.');
      fireEvent.change(taskInput, {
        target: {name: 'task_requiring_ra', value: ''},
      });
      fireEvent.blur(taskInput, {target: {name: 'task_requiring_ra'}});

      expect(screen.getByTestId('error-task_requiring_ra')).toHaveTextContent(
        'This is a mandatory field. Please fill to process.',
      );

      // Fill the field and check error is cleared
      fireEvent.change(taskInput, {
        target: {name: 'task_requiring_ra', value: 'Valid task'},
      });
      expect(screen.getByTestId('error-task_requiring_ra')).toHaveTextContent(
        '',
      );
    });

    it('handles empty string validation correctly', () => {
      renderBasicDetails();

      const taskInput = screen.getByLabelText('Task Requiring R.A.');

      // Set to empty string and trigger blur to show error
      fireEvent.change(taskInput, {
        target: {name: 'task_requiring_ra', value: ''},
      });
      fireEvent.blur(taskInput, {target: {name: 'task_requiring_ra'}});

      expect(screen.getByTestId('error-task_requiring_ra')).toHaveTextContent(
        'This is a mandatory field. Please fill to process.',
      );
    });
  });
});
