.ra-more-filters-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;

  .filter-container {
    width: 100%;
    height: 100%;
    overflow-y: hidden;
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 0px 20px;
  }

  .filter-heading {
    font-weight: 500;
    font-size: 14px;
    line-height: 20px;
    color: #333333;
  }

  .filter-container-content {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }

  .filter-checkbox-container {
    width: 100%;
    height: 100%;
    flex: 1;
    overflow-y: auto;
    .ra-form-check-box {
      font-size: 14px !important;
      font-weight: 400 !important;
      line-height: 20px !important;
      color: #333333 !important;
    }
  }

  .filters-footer {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    padding: 20px 20px 16px;
    width: 100%;
    height: 68px;
    gap: 12px;

    border-top: 1px solid #dee2e6;

    flex: none;
    order: 1;
    align-self: stretch;
    flex-grow: 0;

    .footer-btn-primary {
      cursor: pointer;
      padding: 6px 12px;
      width: 62px;
      height: 32px;

      background: #1f4a70;
      border: 1px solid #1f4a70;
      border-radius: 4px;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: #ffffff;
    }

    .footer-btn-secondary {
      cursor: pointer;
      padding: 4px 8px 4px 6px;
      width: 49px;
      height: 28px;

      border-radius: 4px;

      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      text-decoration-line: underline;

      color: #1f4a70;
    }
  }
}

.more-filters-button {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 6px 12px;
  gap: 2px;

  width: 125px;
  height: 32px;

  background: #ffffff !important;
  border: 1px solid #1f4a70;
  border-radius: 4px;

  .icon {
    width: 20px;
    height: 20px;

    flex: none;
    order: 0;
    flex-grow: 0;
    margin-right: 2px;
    color: #1f4a70;
  }

  .label {
    width: 79px;
    height: 20px;

    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    /* identical to box height, or 143% */
    display: flex;
    align-items: center;
    text-align: center;

    color: #1f4a70;

    /* Inside auto layout */
    flex: none;
    order: 1;
    flex-grow: 0;
  }

  .label-badge {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 2px 8px;

    width: 24px;
    height: 20px;

    background: #edf3f7;
    border-radius: 50px;

    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    margin-left: 12px;

    color: #333333;
  }
}
