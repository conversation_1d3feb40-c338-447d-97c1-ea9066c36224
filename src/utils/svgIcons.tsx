import React from 'react';

export const SortIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" {...props}>
      <path d="M12 3.202l3.839 4.798h-7.678l3.839-4.798zm0-3.202l-8 10h16l-8-10zm3.839 16l-3.839 4.798-3.839-4.798h7.678zm4.161-2h-16l8 10 8-10z" />
    </svg>
  );
};

export const RoundCheckFilled = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="12" cy="12" r="11.5" fill="#0DA666" stroke="#0DA666" />
      <g clipPath="url(#clip0_859_3700)">
        <path
          d="M18.795 7.06441C18.5216 6.79103 18.0784 6.79103 17.805 7.06441L9.4186 15.4509L6.19499 12.2273C5.92163 11.9539 5.47845 11.954 5.20504 12.2273C4.93165 12.5007 4.93165 12.9439 5.20504 13.2172L8.92362 16.9358C9.1969 17.2091 9.64041 17.2089 9.91358 16.9358L18.795 8.05437C19.0684 7.78101 19.0683 7.33779 18.795 7.06441Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_859_3700">
          <rect
            width="14"
            height="14"
            fill="white"
            transform="translate(5 5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const CheckFilled = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect
        x="1"
        y="1"
        width="18"
        height="18"
        rx="2"
        fill="#0091B8"
        stroke="#0091B8"
      />
      <path
        d="M14.5363 6.70882C14.2578 6.43039 13.8402 6.43039 13.5618 6.70882L8.34118 11.9294L6.18333 9.77157C5.9049 9.49314 5.48725 9.49314 5.20882 9.77157C4.93039 10.05 4.93039 10.4676 5.20882 10.7461L7.85392 13.3912C7.99314 13.5304 8.13235 13.6 8.34118 13.6C8.55 13.6 8.68921 13.5304 8.82843 13.3912L14.5363 7.68333C14.8147 7.4049 14.8147 6.98725 14.5363 6.70882Z"
        fill="white"
      />
    </svg>
  );
};
export const CheckUnFilled = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect
        x="1"
        y="1"
        width="18"
        height="18"
        rx="2"
        fill="white"
        stroke="#CCCCCC"
      />
    </svg>
  );
};

export const CommentIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M5.73333 17.9333C5.65493 17.9333 5.576 17.9157 5.50187 17.8805C5.31787 17.7915 5.2 17.6053 5.2 17.4V14.7333H3.6C2.71787 14.7333 2 14.0155 2 13.1333V4.6C2 3.71787 2.71787 3 3.6 3H16.4C17.2821 3 18 3.71787 18 4.6V13.1333C18 14.0155 17.2821 14.7333 16.4 14.7333H9.92053L6.06667 17.8165C5.97013 17.8939 5.85227 17.9333 5.73333 17.9333ZM3.6 4.06667C3.3056 4.06667 3.06667 4.30613 3.06667 4.6V13.1333C3.06667 13.4272 3.3056 13.6667 3.6 13.6667H5.73333C6.02827 13.6667 6.26667 13.9051 6.26667 14.2V16.2907L9.4 13.7835C9.49493 13.7077 9.61173 13.6667 9.73333 13.6667H16.4C16.6944 13.6667 16.9333 13.4272 16.9333 13.1333V4.6C16.9333 4.30613 16.6944 4.06667 16.4 4.06667H3.6Z"
        fill="#1F4A70"
      />
      <path
        d="M14.2659 8.33327H5.73255C5.43762 8.33327 5.19922 8.09433 5.19922 7.79993C5.19922 7.50553 5.43762 7.2666 5.73255 7.2666H14.2659C14.5608 7.2666 14.7992 7.50553 14.7992 7.79993C14.7992 8.09433 14.5608 8.33327 14.2659 8.33327Z"
        fill="#1F4A70"
      />
      <path
        d="M9.99922 10.4671H5.73255C5.43762 10.4671 5.19922 10.2281 5.19922 9.93372C5.19922 9.63932 5.43762 9.40039 5.73255 9.40039H9.99922C10.2942 9.40039 10.5326 9.63932 10.5326 9.93372C10.5326 10.2281 10.2942 10.4671 9.99922 10.4671Z"
        fill="#1F4A70"
      />
    </svg>
  );
};
export const InfoIcon = (props: React.SVGAttributes<SVGElement>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_551_96010)">
      <path
        d="M8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15Z"
        stroke="#6C757D"
      />
      <path
        d="M7.47159 11.5V6.04545H8.53338V11.5H7.47159ZM8.00781 5.20384C7.82315 5.20384 7.66454 5.14228 7.53196 5.01918C7.40175 4.8937 7.33665 4.74455 7.33665 4.57173C7.33665 4.39654 7.40175 4.2474 7.53196 4.12429C7.66454 3.99882 7.82315 3.93608 8.00781 3.93608C8.19247 3.93608 8.34991 3.99882 8.48011 4.12429C8.61269 4.2474 8.67898 4.39654 8.67898 4.57173C8.67898 4.74455 8.61269 4.8937 8.48011 5.01918C8.34991 5.14228 8.19247 5.20384 8.00781 5.20384Z"
        fill="#6C757D"
      />
    </g>
    <defs>
      <clipPath id="clip0_551_96010">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const CalendarIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="14"
      height="14"
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M4.20039 0.700002C4.20039 0.479093 4.0213 0.300002 3.80039 0.300002C3.57948 0.300002 3.40039 0.479093 3.40039 0.700002V1.8H2.00039C1.0615 1.8 0.300391 2.56111 0.300391 3.5V12.1C0.300391 13.0389 1.0615 13.8 2.00039 13.8H12.2004C13.1393 13.8 13.9004 13.0389 13.9004 12.1V3.5C13.9004 2.56111 13.1393 1.8 12.2004 1.8H11.2004V0.700002C11.2004 0.479093 11.0213 0.300002 10.8004 0.300002C10.5795 0.300002 10.4004 0.479093 10.4004 0.700002V1.8H4.20039V0.700002ZM2.00039 2.6H12.2004C12.6974 2.6 13.1004 3.00295 13.1004 3.5V4.5H1.10039V3.5C1.10039 3.00295 1.50334 2.6 2.00039 2.6ZM1.10039 12.1V5.3H13.1004V12.1C13.1004 12.5971 12.6974 13 12.2004 13H2.00039C1.50334 13 1.10039 12.5971 1.10039 12.1Z"
        fill="#505058"
        stroke="#505058"
        strokeWidth="0.2"
      />
    </svg>
  );
};

export const DeleteJobIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_1636_145006)">
        <path
          d="M12.5187 3.08001H10.6355V2.70321C10.6351 2.40375 10.5159 2.11668 10.3042 1.90493C10.0924 1.69318 9.80537 1.57403 9.50591 1.57361L6.49391 1.57361C6.19445 1.57403 5.90738 1.69318 5.69563 1.90493C5.48388 2.11668 5.36473 2.40375 5.36431 2.70321V3.08001H3.48111C3.21235 3.08002 2.9524 3.17586 2.74796 3.35031C2.54351 3.52475 2.40796 3.76638 2.36565 4.03179C2.32334 4.2972 2.37705 4.56899 2.51713 4.79836C2.65721 5.02773 2.87448 5.19964 3.12991 5.28321L3.80191 13.3912C3.82681 13.6731 3.956 13.9355 4.16419 14.1271C4.37237 14.3187 4.64457 14.4257 4.92751 14.4272H11.0715C11.3551 14.4256 11.6278 14.318 11.8361 14.1256C12.0444 13.9332 12.1732 13.6698 12.1971 13.3872L12.8683 5.27921C13.1237 5.19564 13.341 5.02373 13.4811 4.79436C13.6212 4.56499 13.6749 4.2932 13.6326 4.02779C13.5903 3.76238 13.4547 3.52075 13.2503 3.34631C13.0458 3.17186 12.7859 3.07602 12.5171 3.07601L12.5187 3.08001ZM6.11871 2.70321C6.11871 2.60327 6.15841 2.50743 6.22907 2.43677C6.29974 2.36611 6.39558 2.32641 6.49551 2.32641H9.50831C9.60824 2.32641 9.70409 2.36611 9.77475 2.43677C9.84541 2.50743 9.88511 2.60327 9.88511 2.70321V3.08001H6.11711L6.11871 2.70321ZM11.4475 13.328C11.4393 13.422 11.3963 13.5096 11.3269 13.5735C11.2575 13.6375 11.1667 13.6732 11.0723 13.6736H4.92751C4.83327 13.6732 4.74257 13.6376 4.67317 13.5738C4.60378 13.5101 4.56068 13.4227 4.55231 13.3288L3.89071 5.33921H12.1115L11.4475 13.328ZM12.5187 4.58641H3.47871C3.42782 4.58876 3.37698 4.58076 3.32927 4.5629C3.28156 4.54504 3.23797 4.5177 3.20112 4.48251C3.16428 4.44733 3.13496 4.40504 3.11492 4.3582C3.09488 4.31137 3.08455 4.26095 3.08455 4.21001C3.08455 4.15907 3.09488 4.10865 3.11492 4.06181C3.13496 4.01497 3.16428 3.97269 3.20112 3.9375C3.23797 3.90232 3.28156 3.87497 3.32927 3.85712C3.37698 3.83926 3.42782 3.83126 3.47871 3.83361H12.5187C12.5696 3.83126 12.6204 3.83926 12.6681 3.85712C12.7159 3.87497 12.7595 3.90232 12.7963 3.9375C12.8331 3.97269 12.8625 4.01497 12.8825 4.06181C12.9025 4.10865 12.9129 4.15907 12.9129 4.21001C12.9129 4.26095 12.9025 4.31137 12.8825 4.3582C12.8625 4.40504 12.8331 4.44733 12.7963 4.48251C12.7595 4.5177 12.7159 4.54504 12.6681 4.5629C12.6204 4.58076 12.5696 4.58876 12.5187 4.58641Z"
          fill="#1F4A70"
        />
        <path
          d="M6.4924 12.5208L6.11559 6.44562C6.10743 6.34741 6.06113 6.2563 5.9866 6.19182C5.91207 6.12735 5.81525 6.09465 5.71688 6.10072C5.61852 6.10679 5.52646 6.15115 5.46041 6.22429C5.39437 6.29744 5.35961 6.39355 5.36359 6.49202L5.7404 12.5672C5.74242 12.6173 5.75443 12.6665 5.77572 12.712C5.79701 12.7574 5.82715 12.7981 5.86438 12.8317C5.90161 12.8653 5.94518 12.8911 5.99252 12.9077C6.03986 12.9242 6.09003 12.9312 6.14009 12.9281C6.19015 12.925 6.2391 12.9119 6.28405 12.8897C6.329 12.8674 6.36904 12.8364 6.40186 12.7985C6.43467 12.7606 6.45958 12.7165 6.47512 12.6688C6.49067 12.6211 6.49655 12.5708 6.4924 12.5208Z"
          fill="#1F4A70"
        />
        <path
          d="M8.00179 6.09201C7.90186 6.09201 7.80603 6.13171 7.73536 6.20237C7.6647 6.27304 7.625 6.36887 7.625 6.4688V12.544C7.62947 12.6408 7.67107 12.7322 7.74117 12.7992C7.81127 12.8661 7.90448 12.9034 8.0014 12.9034C8.09833 12.9034 8.19153 12.8661 8.26163 12.7992C8.33173 12.7322 8.37333 12.6408 8.3778 12.544V6.4688C8.3778 6.36901 8.33822 6.2733 8.26772 6.20266C8.19723 6.13201 8.10159 6.09222 8.00179 6.09201Z"
          fill="#1F4A70"
        />
        <path
          d="M10.2833 6.09201C10.2339 6.08886 10.1843 6.0955 10.1374 6.11154C10.0906 6.12757 10.0473 6.1527 10.0102 6.18546C9.97302 6.21823 9.94268 6.258 9.92091 6.30251C9.89915 6.34701 9.88637 6.39536 9.88332 6.4448L9.50812 12.52C9.50414 12.6185 9.53891 12.7146 9.60495 12.7877C9.67099 12.8609 9.76305 12.9052 9.86142 12.9113C9.95978 12.9174 10.0566 12.8847 10.1311 12.8202C10.2056 12.7557 10.252 12.6646 10.2601 12.5664L10.6369 6.4912C10.64 6.44176 10.6332 6.39222 10.6171 6.34539C10.6009 6.29856 10.5757 6.25537 10.5429 6.21829C10.51 6.18121 10.4702 6.15098 10.4257 6.12931C10.3811 6.10764 10.3328 6.09496 10.2833 6.09201Z"
          fill="#1F4A70"
        />
      </g>
      <defs>
        <clipPath id="clip0_1636_145006">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const JobCardArrowUpIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_755_140002)">
        <path
          d="M14.3047 12.184L9.91553 7.81299L5.69263 12.184"
          stroke="#1F4A70"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_755_140002">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
export const JobCardArrowDownIcon = (
  props: React.SVGAttributes<SVGElement>,
) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_326_128851)">
        <path
          d="M5.69141 7.81299L10.0806 12.184L14.3035 7.81299"
          stroke="#1F4A70"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_326_128851">
          <rect width="20" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};
export const EditFormDetailIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_1722_103625)">
        <path
          d="M12.5186 2.12359L12.6378 2.00439C12.7275 1.91472 12.834 1.8436 12.9512 1.7951C13.0684 1.74659 13.194 1.72164 13.3209 1.72168C13.4477 1.72172 13.5733 1.74674 13.6905 1.79531C13.8077 1.84389 13.9141 1.91507 14.0038 2.00479C14.0935 2.09451 14.1646 2.20101 14.2131 2.31821C14.2616 2.43542 14.2865 2.56103 14.2865 2.68787C14.2865 2.81472 14.2615 2.94031 14.2129 3.05749C14.1643 3.17466 14.0931 3.28112 14.0034 3.37079L13.8786 3.48359C14.0825 3.71228 14.1912 4.0103 14.1824 4.31657C14.1737 4.62284 14.0481 4.91415 13.8314 5.13079L5.6018 13.3668C5.55033 13.418 5.4859 13.4542 5.4154 13.4716L2.2154 14.2716C2.14844 14.2883 2.0783 14.2873 2.0118 14.2689C1.9453 14.2504 1.88471 14.2151 1.83591 14.1663C1.78711 14.1175 1.75177 14.0569 1.73331 13.9904C1.71486 13.9239 1.71393 13.8538 1.7306 13.7868L2.5306 10.5868C2.54811 10.5166 2.58433 10.4524 2.6354 10.4012L10.349 2.68359C10.2719 2.62929 10.178 2.60401 10.084 2.61221C9.99004 2.6204 9.90198 2.66155 9.8354 2.72839L7.2018 5.36679C7.12669 5.4419 7.02482 5.48409 6.9186 5.48409C6.81238 5.48409 6.71051 5.4419 6.6354 5.36679C6.56029 5.29168 6.5181 5.18981 6.5181 5.08359C6.5181 4.97737 6.56029 4.8755 6.6354 4.80039L9.2706 2.16679C9.4878 1.95045 9.77956 1.82543 10.086 1.8174C10.3925 1.80937 10.6904 1.91892 10.9186 2.12359C11.1386 1.92681 11.4234 1.81801 11.7186 1.81801C12.0138 1.81801 12.2986 1.92681 12.5186 2.12359ZM12.0034 2.73639C11.9284 2.6614 11.8267 2.61927 11.7206 2.61927C11.6145 2.61927 11.5128 2.6614 11.4378 2.73639L3.2786 10.8884L2.6674 13.334L5.113 12.7228L13.2706 4.56679C13.3079 4.52963 13.3374 4.48549 13.3576 4.43689C13.3777 4.3883 13.3881 4.3362 13.3881 4.28359C13.3881 4.23097 13.3777 4.17888 13.3576 4.13028C13.3374 4.08168 13.3079 4.03754 13.2706 4.00039L12.0026 2.73239L12.0034 2.73639Z"
          fill="#1F4A70"
        />
      </g>
      <defs>
        <clipPath id="clip0_1722_103625">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export const ActionMenuIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M3.6 11.6004C4.48366 11.6004 5.2 10.884 5.2 10.0004C5.2 9.11673 4.48366 8.40039 3.6 8.40039C2.71634 8.40039 2 9.11673 2 10.0004C2 10.884 2.71634 11.6004 3.6 11.6004Z"
        fill="#1C1F4A"
      />
      <path
        d="M9.99844 11.6004C10.8821 11.6004 11.5984 10.884 11.5984 10.0004C11.5984 9.11673 10.8821 8.40039 9.99844 8.40039C9.11478 8.40039 8.39844 9.11673 8.39844 10.0004C8.39844 10.884 9.11478 11.6004 9.99844 11.6004Z"
        fill="#1C1F4A"
      />
      <path
        d="M16.3969 11.6004C17.2805 11.6004 17.9969 10.884 17.9969 10.0004C17.9969 9.11673 17.2805 8.40039 16.3969 8.40039C15.5132 8.40039 14.7969 9.11673 14.7969 10.0004C14.7969 10.884 15.5132 11.6004 16.3969 11.6004Z"
        fill="#1C1F4A"
      />
    </svg>
  );
};
export const RadioUncheckedIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="16"
      height="20"
      viewBox="0 0 16 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect
        x="0.5"
        y="2.5"
        width="15"
        height="15"
        rx="7.5"
        fill="white"
        stroke="#CCCCCC"
      />
    </svg>
  );
};
export const RadioCheckedIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="16"
      height="20"
      viewBox="0 0 16 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect y="2" width="16" height="16" rx="8" fill="#0091B8" />
      <rect x="4" y="6" width="8" height="8" rx="4" fill="white" />
    </svg>
  );
};

export const JobAlertIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_2620_85144)">
        <path
          d="M22 6H10C7.79086 6 6 7.79086 6 10V22C6 24.2091 7.79086 26 10 26H22C24.2091 26 26 24.2091 26 22V10C26 7.79086 24.2091 6 22 6Z"
          fill="#FFF9E8"
        />
        <path
          d="M21.8063 19.2668L17.2203 11.3228C17.0965 11.1082 16.9184 10.9301 16.7039 10.8062C16.4894 10.6824 16.246 10.6172 15.9983 10.6172C15.7506 10.6172 15.5073 10.6824 15.2928 10.8062C15.0783 10.9301 14.9002 11.1082 14.7763 11.3228L10.1893 19.2668C10.0654 19.4813 10.0001 19.7246 10 19.9724C9.99994 20.2202 10.0651 20.4636 10.189 20.6781C10.3128 20.8927 10.491 21.0709 10.7056 21.1948C10.9202 21.3186 11.1636 21.3838 11.4113 21.3838H20.5843C20.8321 21.3838 21.0755 21.3186 21.2901 21.1948C21.5046 21.0709 21.6828 20.8927 21.8067 20.6781C21.9305 20.4636 21.9957 20.2202 21.9957 19.9724C21.9956 19.7246 21.9303 19.4813 21.8063 19.2668ZM21.1973 20.3268C21.1358 20.4349 21.0466 20.5247 20.9389 20.5869C20.8311 20.6492 20.7087 20.6816 20.5843 20.6808H11.4113C11.2869 20.6808 11.1647 20.6481 11.0569 20.5859C10.9492 20.5237 10.8597 20.4342 10.7976 20.3264C10.7354 20.2187 10.7028 20.0964 10.7029 19.972C10.703 19.8476 10.7359 19.7254 10.7983 19.6178L15.3853 11.6738C15.4475 11.5662 15.5369 11.4769 15.6445 11.4148C15.7521 11.3527 15.8741 11.32 15.9983 11.32C16.1226 11.32 16.2446 11.3527 16.3522 11.4148C16.4598 11.4769 16.5492 11.5662 16.6113 11.6738L21.1983 19.6178C21.2613 19.7251 21.2944 19.8473 21.2944 19.9718C21.2944 20.0962 21.2613 20.2184 21.1983 20.3258L21.1973 20.3268Z"
          fill="#F08100"
          stroke="#F08100"
          strokeWidth="0.4"
        />
        <path
          d="M16.3514 14.125H15.6484V17.64H16.3514V14.125Z"
          fill="#F08100"
          stroke="#F08100"
          strokeWidth="0.4"
        />
        <path
          d="M16.0002 18.3438C15.9075 18.3437 15.8168 18.3713 15.7397 18.4228C15.6626 18.4743 15.6024 18.5476 15.5669 18.6333C15.5315 18.719 15.5222 18.8133 15.5403 18.9043C15.5584 18.9952 15.603 19.0788 15.6686 19.1444C15.7342 19.21 15.8178 19.2546 15.9087 19.2727C15.9997 19.2908 16.094 19.2816 16.1797 19.2461C16.2654 19.2106 16.3387 19.1505 16.3902 19.0733C16.4417 18.9962 16.4692 18.9055 16.4692 18.8128C16.4692 18.6884 16.4198 18.5691 16.3319 18.4811C16.2439 18.3932 16.1246 18.3438 16.0002 18.3438Z"
          fill="#F08100"
          stroke="#F08100"
          strokeWidth="0.4"
        />
      </g>
      <defs>
        <clipPath id="clip0_2620_85144">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(6 6)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const CrewIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="73"
      height="72"
      viewBox="0 0 73 72"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M36.5125 71.9999C28.1841 72.0028 20.1124 69.118 13.6725 63.837C7.23257 58.556 2.82288 51.2055 1.19471 43.0379C-0.433468 34.8702 0.820599 26.3907 4.74325 19.044C8.6659 11.6973 15.0145 5.93787 22.7073 2.74702C30.4002 -0.443829 38.9614 -0.86872 46.9324 1.54474C54.9033 3.95819 61.791 9.06069 66.4218 15.9829C71.0526 22.9051 73.1401 31.2188 72.3287 39.5075C71.5172 47.7962 67.857 55.5472 61.9716 61.4399C58.6537 64.791 54.7035 67.45 50.3501 69.2627C45.9966 71.0754 41.3266 72.0058 36.6109 71.9999H36.5125ZM27.0589 35.568C23.8067 35.6549 20.7171 37.0084 18.4483 39.34C16.1794 41.6716 14.9107 44.797 14.9125 48.0503C14.9125 48.1367 14.9125 48.2207 14.9125 48.3071V57.3935C14.9122 58.5392 15.3603 59.6395 16.1609 60.4589C16.9616 61.2784 18.0511 61.752 19.1965 61.7783H53.8141C54.9654 61.7483 56.0593 61.2694 56.8624 60.4439C57.6655 59.6183 58.1141 58.5116 58.1124 57.3599V48.2951C58.1124 48.2279 58.1124 48.1463 58.1124 48.0671C58.1093 44.8151 56.8386 41.6924 54.5703 39.3621C52.3019 37.0319 49.2144 35.6777 45.9637 35.5872H43.3237V33.2664C43.7605 32.7864 44.1685 32.376 44.5621 31.9776L44.6893 31.8552C45.6203 31.1166 46.3825 30.1872 46.9244 29.1295C47.4663 28.0718 47.7753 26.9103 47.8309 25.7232V25.704C48.4857 25.2962 49.0364 24.7414 49.4393 24.0836C49.8422 23.4257 50.0861 22.683 50.1517 21.9144V21.8664C50.1515 21.1167 49.9773 20.3773 49.6429 19.7064L49.6573 19.7328C50.1621 19.4054 50.5207 18.8954 50.6581 18.3096V18.288L52.3381 14.928C52.3862 14.7357 52.4104 14.5382 52.4101 14.34C52.4113 13.8146 52.2429 13.3028 51.9301 12.8808C51.7241 12.5976 51.4546 12.3665 51.1433 12.206C50.832 12.0456 50.4875 11.9602 50.1373 11.9568H44.0101C43.3132 10.5311 42.2311 9.32892 40.8864 8.48635C39.5416 7.64378 37.9879 7.19444 36.401 7.1892C34.8141 7.18395 33.2574 7.62301 31.9071 8.45667C30.5568 9.29034 29.4668 10.4853 28.7605 11.9064L28.7389 11.9568H22.8661C22.5174 11.9585 22.1741 12.0427 21.8642 12.2024C21.5543 12.3622 21.2866 12.593 21.0829 12.876C20.7705 13.3019 20.6023 13.8166 20.6029 14.3448C20.603 14.5428 20.6263 14.7402 20.6725 14.9328V14.916L22.3741 18.276C22.5088 18.8668 22.8665 19.3827 23.3725 19.716C23.0343 20.3857 22.8592 21.1258 22.8613 21.876C22.9251 22.6451 23.1677 23.3888 23.5698 24.0476C23.9719 24.7063 24.5224 25.262 25.1773 25.6704H25.1965C25.2496 26.8568 25.5554 28.0182 26.0935 29.0769C26.6315 30.1356 27.3894 31.0673 28.3165 31.8096L28.3333 31.824L28.4557 31.9464C28.8493 32.3448 29.2573 32.7528 29.6893 33.2304V35.5512L27.0589 35.568ZM47.8837 59.3855H25.1413V50.8535C25.1413 50.8367 25.1413 50.8199 25.1413 50.8007C25.1413 50.4981 25.0211 50.2078 24.8071 49.9938C24.593 49.7798 24.3028 49.6595 24.0001 49.6595C23.6974 49.6595 23.4072 49.7798 23.1932 49.9938C22.9791 50.2078 22.8589 50.4981 22.8589 50.8007C22.8577 50.8191 22.8577 50.8376 22.8589 50.8559V59.3975H19.2037C18.6662 59.3856 18.1546 59.1637 17.7787 58.7792C17.4028 58.3948 17.1924 57.8784 17.1925 57.3407V57.3047V48.2399C17.1874 45.6744 18.1272 43.1968 19.8325 41.2799L33.6973 46.1591C33.1559 46.6094 32.7203 47.1733 32.4213 47.8108C32.1222 48.4483 31.9671 49.1438 31.9669 49.8479V49.8863C31.3847 49.889 30.8093 50.0116 30.2766 50.2466C29.7439 50.4815 29.2654 50.8238 28.8709 51.2519C27.6783 52.9738 27.1779 55.0817 27.4693 57.1559V57.1127C27.4881 57.376 27.5981 57.6245 27.7803 57.8155C27.9625 58.0065 28.2056 58.128 28.4677 58.1591C28.8413 58.2026 29.2172 58.2243 29.5933 58.2239H29.6125C29.8321 58.2529 30.0534 58.2673 30.2749 58.2671C31.4051 58.267 32.5041 57.8966 33.4039 57.2126C34.3037 56.5287 34.9547 55.5689 35.2573 54.4799V54.4439C35.6512 54.5751 36.0637 54.6416 36.4789 54.6407H36.5125C36.9463 54.6415 37.3773 54.5726 37.7893 54.4367H37.7605C38.0547 55.5361 38.7029 56.5079 39.6048 57.202C40.5067 57.896 41.612 58.2738 42.7501 58.2767C42.9837 58.2767 43.217 58.2607 43.4485 58.2287H43.4725C43.8518 58.2287 44.2308 58.2071 44.6077 58.1639H44.5621C44.8221 58.1285 45.0625 58.0064 45.2446 57.8174C45.4266 57.6283 45.5396 57.3835 45.5653 57.1223C45.6165 56.7509 45.6421 56.3764 45.6421 56.0015C45.6448 54.2962 45.121 52.6315 44.1421 51.2351L44.1589 51.2615C43.7642 50.8334 43.2857 50.491 42.7531 50.2557C42.2205 50.0203 41.6452 49.8971 41.0629 49.8935V49.8575C41.0628 49.1548 40.9085 48.4606 40.6107 47.824C40.313 47.1874 39.8791 46.624 39.3397 46.1735L53.1901 41.2799C54.8952 43.1969 55.8349 45.6744 55.8301 48.2399V48.2975V57.3407C55.8302 57.8788 55.6194 58.3955 55.243 58.78C54.8666 59.1645 54.3544 59.3862 53.8165 59.3975H50.1445V50.8559C50.1445 50.8391 50.1445 50.8223 50.1445 50.8031C50.1445 50.5017 50.0247 50.2127 49.8116 49.9996C49.5985 49.7865 49.3095 49.6667 49.0081 49.6667C48.7067 49.6667 48.4176 49.7865 48.2045 49.9996C47.9914 50.2127 47.8717 50.5017 47.8717 50.8031C47.8706 50.8207 47.8706 50.8383 47.8717 50.8559V59.4023L47.8837 59.3855ZM36.5125 43.6343L31.9525 36.4944V34.752C32.5283 35.3844 33.2298 35.8897 34.0121 36.2354C34.7945 36.5812 35.6404 36.7598 36.4957 36.7598C37.351 36.7598 38.1969 36.5812 38.9792 36.2354C39.7615 35.8897 40.4631 35.3844 41.0389 34.752V36.4967L36.5125 43.6343ZM36.5125 34.3848C35.2141 34.3848 32.5933 33.8184 29.6869 30.0384L29.5933 29.916L29.5789 29.9016C28.2342 28.5563 27.4605 26.744 27.4189 24.8424C27.4193 24.5587 27.3169 24.2845 27.1307 24.0705C26.9445 23.8565 26.6871 23.7172 26.4061 23.6784C26.2045 23.6784 25.2061 23.5896 25.1461 21.8976C25.1461 20.9376 25.6813 20.112 26.2861 20.112H46.7365C47.3413 20.112 47.8765 20.9472 47.8765 21.8976C47.8069 23.3976 47.1877 23.5992 46.6069 23.6784C46.327 23.7167 46.0701 23.8539 45.8826 24.0652C45.695 24.2765 45.5893 24.5479 45.5845 24.8304C45.5562 26.724 44.7908 28.532 43.4509 29.8704L43.3189 30.0336C40.4101 33.8112 37.7989 34.3776 36.5053 34.3776L36.5125 34.3848Z"
        fill="#1F4A70"
      />
    </svg>
  );
};

export const ExclaimationIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="48"
      height="49"
      viewBox="0 0 48 49"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M24 45.5C35.598 45.5 45 36.098 45 24.5C45 12.902 35.598 3.5 24 3.5C12.402 3.5 3 12.902 3 24.5C3 36.098 12.402 45.5 24 45.5Z"
        stroke="#1F4A70"
        stroke-width="3"
      />
      <path
        d="M22.407 34V17.6364H25.5923V34H22.407ZM24.0156 15.1115C23.4616 15.1115 22.9858 14.9268 22.5881 14.5575C22.1974 14.1811 22.0021 13.7337 22.0021 13.2152C22.0021 12.6896 22.1974 12.2422 22.5881 11.8729C22.9858 11.4964 23.4616 11.3082 24.0156 11.3082C24.5696 11.3082 25.0419 11.4964 25.4325 11.8729C25.8303 12.2422 26.0291 12.6896 26.0291 13.2152C26.0291 13.7337 25.8303 14.1811 25.4325 14.5575C25.0419 14.9268 24.5696 15.1115 24.0156 15.1115Z"
        fill="#1F4A70"
      />
    </svg>
  );
};

export const ProfileIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="302"
      height="56"
      viewBox="0 0 302 56"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle cx="32" cy="28" r="24" fill="#E5F4F8" />
      <path
        d="M24.1364 34H22.2727L26.4602 22.3636H28.4886L32.6761 34H30.8125L27.5227 24.4773H27.4318L24.1364 34ZM24.4489 29.4432H30.4943V30.9205H24.4489V29.4432ZM32.4077 23.875V22.3636H41.4134V23.875H37.7827V34H36.0327V23.875H32.4077Z"
        fill="#1F4A70"
      />
      <path
        d="M70.2614 24H68.3977L72.5852 12.3636H74.6136L78.8011 24H76.9375L73.6477 14.4773H73.5568L70.2614 24ZM70.5739 19.4432H76.6193V20.9205H70.5739V19.4432ZM82.044 12.3636V24H80.3452V12.3636H82.044ZM88.0909 24.1761C87.2311 24.1761 86.4905 23.9924 85.8693 23.625C85.2519 23.2538 84.7746 22.733 84.4375 22.0625C84.1042 21.3883 83.9375 20.5985 83.9375 19.6932C83.9375 18.7992 84.1042 18.0114 84.4375 17.3295C84.7746 16.6477 85.2443 16.1155 85.8466 15.733C86.4527 15.3504 87.161 15.1591 87.9716 15.1591C88.464 15.1591 88.9413 15.2405 89.4034 15.4034C89.8655 15.5663 90.2803 15.822 90.6477 16.1705C91.0152 16.5189 91.3049 16.9716 91.517 17.5284C91.7292 18.0814 91.8352 18.7538 91.8352 19.5455V20.1477H84.8977V18.875H90.1705C90.1705 18.428 90.0795 18.0322 89.8977 17.6875C89.7159 17.339 89.4602 17.0644 89.1307 16.8636C88.8049 16.6629 88.4223 16.5625 87.983 16.5625C87.5057 16.5625 87.089 16.6799 86.733 16.9148C86.3807 17.1458 86.108 17.4489 85.9148 17.8239C85.7254 18.1951 85.6307 18.5985 85.6307 19.0341V20.0284C85.6307 20.6117 85.733 21.108 85.9375 21.517C86.1458 21.9261 86.4356 22.2386 86.8068 22.4545C87.178 22.6667 87.6117 22.7727 88.108 22.7727C88.4299 22.7727 88.7235 22.7273 88.9886 22.6364C89.2538 22.5417 89.483 22.4015 89.6761 22.2159C89.8693 22.0303 90.017 21.8011 90.1193 21.5284L91.7273 21.8182C91.5985 22.2917 91.3674 22.7064 91.0341 23.0625C90.7045 23.4148 90.2898 23.6894 89.7898 23.8864C89.2936 24.0795 88.7273 24.1761 88.0909 24.1761ZM94.858 15.2727L96.7841 18.6705L98.7273 15.2727H100.585L97.8636 19.6364L100.608 24H98.75L96.7841 20.7386L94.8239 24H92.9602L95.6761 19.6364L92.9943 15.2727H94.858ZM106.126 13.875V12.3636H115.132V13.875H111.501V24H109.751V13.875H106.126ZM118.685 18.8182V24H116.986V12.3636H118.662V16.6932H118.77C118.974 16.2235 119.287 15.8504 119.707 15.5739C120.128 15.2973 120.677 15.1591 121.355 15.1591C121.954 15.1591 122.476 15.2822 122.923 15.5284C123.374 15.7746 123.723 16.142 123.969 16.6307C124.219 17.1155 124.344 17.7216 124.344 18.4489V24H122.645V18.6534C122.645 18.0133 122.48 17.517 122.151 17.1648C121.821 16.8087 121.363 16.6307 120.776 16.6307C120.374 16.6307 120.014 16.7159 119.696 16.8864C119.382 17.0568 119.134 17.3068 118.952 17.6364C118.774 17.9621 118.685 18.3561 118.685 18.8182ZM130.287 24.1761C129.469 24.1761 128.755 23.9886 128.145 23.6136C127.535 23.2386 127.062 22.714 126.724 22.0398C126.387 21.3655 126.219 20.5777 126.219 19.6761C126.219 18.7708 126.387 17.9792 126.724 17.3011C127.062 16.6231 127.535 16.0966 128.145 15.7216C128.755 15.3466 129.469 15.1591 130.287 15.1591C131.105 15.1591 131.819 15.3466 132.429 15.7216C133.039 16.0966 133.512 16.6231 133.849 17.3011C134.187 17.9792 134.355 18.7708 134.355 19.6761C134.355 20.5777 134.187 21.3655 133.849 22.0398C133.512 22.714 133.039 23.2386 132.429 23.6136C131.819 23.9886 131.105 24.1761 130.287 24.1761ZM130.293 22.75C130.823 22.75 131.262 22.6098 131.611 22.3295C131.959 22.0492 132.217 21.6761 132.384 21.2102C132.554 20.7443 132.639 20.2311 132.639 19.6705C132.639 19.1136 132.554 18.6023 132.384 18.1364C132.217 17.6667 131.959 17.2898 131.611 17.0057C131.262 16.7216 130.823 16.5795 130.293 16.5795C129.759 16.5795 129.315 16.7216 128.963 17.0057C128.615 17.2898 128.355 17.6667 128.185 18.1364C128.018 18.6023 127.935 19.1136 127.935 19.6705C127.935 20.2311 128.018 20.7443 128.185 21.2102C128.355 21.6761 128.615 22.0492 128.963 22.3295C129.315 22.6098 129.759 22.75 130.293 22.75ZM136.251 24V15.2727H137.882V16.6932H137.99C138.172 16.2121 138.469 15.8371 138.882 15.5682C139.295 15.2955 139.789 15.1591 140.365 15.1591C140.948 15.1591 141.437 15.2955 141.831 15.5682C142.229 15.8409 142.522 16.2159 142.712 16.6932H142.803C143.011 16.2273 143.342 15.8561 143.797 15.5795C144.251 15.2992 144.793 15.1591 145.422 15.1591C146.214 15.1591 146.859 15.4072 147.359 15.9034C147.863 16.3996 148.115 17.1477 148.115 18.1477V24H146.416V18.3068C146.416 17.7159 146.255 17.2879 145.933 17.0227C145.611 16.7576 145.227 16.625 144.78 16.625C144.227 16.625 143.797 16.7955 143.49 17.1364C143.183 17.4735 143.03 17.9072 143.03 18.4375V24H141.337V18.1989C141.337 17.7254 141.189 17.3447 140.893 17.0568C140.598 16.7689 140.214 16.625 139.74 16.625C139.418 16.625 139.121 16.7102 138.848 16.8807C138.579 17.0473 138.361 17.2803 138.195 17.5795C138.032 17.8788 137.95 18.2254 137.95 18.6193V24H136.251ZM152.932 24.1932C152.379 24.1932 151.879 24.0909 151.432 23.8864C150.985 23.678 150.631 23.3769 150.369 22.983C150.112 22.589 149.983 22.1061 149.983 21.5341C149.983 21.0417 150.078 20.6364 150.267 20.3182C150.456 20 150.712 19.7481 151.034 19.5625C151.356 19.3769 151.716 19.2367 152.114 19.142C152.511 19.0473 152.917 18.9754 153.33 18.9261C153.852 18.8655 154.277 18.8163 154.602 18.7784C154.928 18.7367 155.165 18.6705 155.312 18.5795C155.46 18.4886 155.534 18.3409 155.534 18.1364V18.0966C155.534 17.6004 155.394 17.2159 155.114 16.9432C154.837 16.6705 154.424 16.5341 153.875 16.5341C153.303 16.5341 152.852 16.661 152.523 16.9148C152.197 17.1648 151.972 17.4432 151.847 17.75L150.25 17.3864C150.439 16.8561 150.716 16.428 151.08 16.1023C151.447 15.7727 151.869 15.5341 152.347 15.3864C152.824 15.2348 153.326 15.1591 153.852 15.1591C154.201 15.1591 154.57 15.2008 154.96 15.2841C155.354 15.3636 155.722 15.5114 156.062 15.7273C156.407 15.9432 156.689 16.2519 156.909 16.6534C157.129 17.0511 157.239 17.5682 157.239 18.2045V24H155.58V22.8068H155.511C155.402 23.0265 155.237 23.2424 155.017 23.4545C154.797 23.6667 154.515 23.8428 154.17 23.983C153.826 24.1231 153.413 24.1932 152.932 24.1932ZM153.301 22.8295C153.771 22.8295 154.172 22.7367 154.506 22.5511C154.843 22.3655 155.098 22.1231 155.273 21.8239C155.451 21.5208 155.54 21.197 155.54 20.8523V19.7273C155.479 19.7879 155.362 19.8447 155.188 19.8977C155.017 19.947 154.822 19.9905 154.602 20.0284C154.383 20.0625 154.169 20.0947 153.96 20.125C153.752 20.1515 153.578 20.1742 153.438 20.1932C153.108 20.2348 152.807 20.3049 152.534 20.4034C152.265 20.5019 152.049 20.6439 151.886 20.8295C151.727 21.0114 151.648 21.2538 151.648 21.5568C151.648 21.9773 151.803 22.2955 152.114 22.5114C152.424 22.7235 152.82 22.8295 153.301 22.8295ZM166.041 17.4034L164.501 17.6761C164.437 17.4792 164.335 17.2917 164.195 17.1136C164.058 16.9356 163.873 16.7898 163.638 16.6761C163.403 16.5625 163.109 16.5057 162.757 16.5057C162.276 16.5057 161.875 16.6136 161.553 16.8295C161.231 17.0417 161.07 17.3163 161.07 17.6534C161.07 17.9451 161.178 18.1799 161.393 18.358C161.609 18.536 161.958 18.6818 162.439 18.7955L163.825 19.1136C164.628 19.2992 165.227 19.5852 165.621 19.9716C166.015 20.358 166.212 20.8598 166.212 21.4773C166.212 22 166.06 22.4659 165.757 22.875C165.458 23.2803 165.039 23.5985 164.501 23.8295C163.967 24.0606 163.348 24.1761 162.643 24.1761C161.666 24.1761 160.869 23.9678 160.251 23.5511C159.634 23.1307 159.255 22.5341 159.115 21.7614L160.757 21.5114C160.859 21.9394 161.07 22.2633 161.388 22.483C161.706 22.6989 162.121 22.8068 162.632 22.8068C163.189 22.8068 163.634 22.6913 163.967 22.4602C164.301 22.2254 164.467 21.9394 164.467 21.6023C164.467 21.3295 164.365 21.1004 164.161 20.9148C163.96 20.7292 163.651 20.589 163.234 20.4943L161.757 20.1705C160.943 19.9848 160.34 19.6894 159.95 19.2841C159.564 18.8788 159.371 18.3655 159.371 17.7443C159.371 17.2292 159.515 16.7784 159.803 16.392C160.09 16.0057 160.488 15.7045 160.996 15.4886C161.503 15.2689 162.085 15.1591 162.74 15.1591C163.683 15.1591 164.426 15.3636 164.967 15.7727C165.509 16.178 165.867 16.7216 166.041 17.4034Z"
        fill="#333333"
      />
      <path
        d="M69.233 34.8182H70.7045L74.1648 43.2699H74.2841L77.7443 34.8182H79.2159V45H78.0625V37.2642H77.9631L74.7812 45H73.6676L70.4858 37.2642H70.3864V45H69.233V34.8182ZM83.7761 45.179C83.2922 45.179 82.853 45.0878 82.4586 44.9055C82.0642 44.7199 81.751 44.4531 81.519 44.1051C81.287 43.7538 81.171 43.3295 81.171 42.8324C81.171 42.3949 81.2572 42.0402 81.4295 41.7685C81.6019 41.4934 81.8322 41.2779 82.1206 41.1222C82.4089 40.9664 82.7271 40.8504 83.0751 40.7741C83.4264 40.6946 83.7794 40.6316 84.1341 40.5852C84.5981 40.5256 84.9743 40.4808 85.2626 40.451C85.5543 40.4179 85.7664 40.3632 85.899 40.2869C86.0349 40.2107 86.1028 40.0781 86.1028 39.8892V39.8494C86.1028 39.3589 85.9686 38.9777 85.7001 38.706C85.435 38.4342 85.0323 38.2983 84.492 38.2983C83.9319 38.2983 83.4927 38.4209 83.1745 38.6662C82.8564 38.9115 82.6326 39.1733 82.5034 39.4517L81.3897 39.054C81.5886 38.59 81.8538 38.2287 82.1852 37.9702C82.5199 37.7083 82.8845 37.526 83.2789 37.4233C83.6767 37.3172 84.0678 37.2642 84.4522 37.2642C84.6975 37.2642 84.9792 37.294 85.2974 37.3537C85.6189 37.41 85.9288 37.5277 86.2271 37.7067C86.5287 37.8857 86.7789 38.1558 86.9778 38.517C87.1767 38.8783 87.2761 39.3622 87.2761 39.9688V45H86.1028V43.9659H86.0431C85.9636 44.1316 85.831 44.3089 85.6454 44.4979C85.4598 44.6868 85.2129 44.8475 84.9047 44.9801C84.5964 45.1127 84.2202 45.179 83.7761 45.179ZM83.9551 44.125C84.4191 44.125 84.8102 44.0339 85.1284 43.8516C85.4499 43.6693 85.6918 43.4339 85.8542 43.1456C86.0199 42.8572 86.1028 42.554 86.1028 42.2358V41.1619C86.0531 41.2216 85.9437 41.2763 85.7747 41.326C85.609 41.3724 85.4167 41.4138 85.198 41.4503C84.9825 41.4834 84.7721 41.5133 84.5666 41.5398C84.3644 41.563 84.2003 41.5829 84.0744 41.5994C83.7695 41.6392 83.4844 41.7038 83.2193 41.7933C82.9574 41.8795 82.7453 42.0104 82.5829 42.1861C82.4238 42.3584 82.3443 42.5937 82.3443 42.892C82.3443 43.2997 82.4951 43.608 82.7967 43.8168C83.1016 44.0223 83.4877 44.125 83.9551 44.125ZM94.8267 39.0739L93.7727 39.3722C93.7064 39.1965 93.6087 39.0258 93.4794 38.8601C93.3535 38.6911 93.1811 38.5518 92.9624 38.4425C92.7436 38.3331 92.4635 38.2784 92.1222 38.2784C91.6548 38.2784 91.2654 38.3861 90.9538 38.6016C90.6456 38.8137 90.4915 39.0838 90.4915 39.4119C90.4915 39.7036 90.5975 39.9339 90.8097 40.103C91.0218 40.272 91.3532 40.4129 91.804 40.5256L92.9375 40.804C93.6203 40.9697 94.129 41.2232 94.4638 41.5646C94.7985 41.9027 94.9659 42.3385 94.9659 42.8722C94.9659 43.3097 94.84 43.7008 94.5881 44.0455C94.3395 44.3902 93.9915 44.6619 93.544 44.8608C93.0966 45.0597 92.5762 45.1591 91.983 45.1591C91.2041 45.1591 90.5594 44.9901 90.049 44.652C89.5386 44.3139 89.2154 43.8201 89.0795 43.1705L90.1932 42.892C90.2992 43.303 90.4998 43.6113 90.7947 43.8168C91.093 44.0223 91.4825 44.125 91.9631 44.125C92.5099 44.125 92.9441 44.009 93.2656 43.777C93.5904 43.5417 93.7528 43.2599 93.7528 42.9318C93.7528 42.6667 93.66 42.4446 93.4744 42.2656C93.2888 42.0833 93.0038 41.9474 92.6193 41.858L91.3466 41.5597C90.6473 41.3939 90.1335 41.1371 89.8054 40.7891C89.4806 40.4377 89.3182 39.9986 89.3182 39.4716C89.3182 39.0407 89.4392 38.6596 89.6811 38.3281C89.9264 37.9967 90.2595 37.7365 90.6804 37.5476C91.1046 37.3587 91.5852 37.2642 92.1222 37.2642C92.8778 37.2642 93.4711 37.4299 93.902 37.7614C94.3362 38.0928 94.6444 38.5303 94.8267 39.0739ZM100.053 37.3636V38.358H96.0957V37.3636H100.053ZM97.2491 35.5341H98.4224V42.8125C98.4224 43.1439 98.4705 43.3925 98.5666 43.5582C98.666 43.7206 98.792 43.83 98.9444 43.8864C99.1002 43.9394 99.2643 43.9659 99.4366 43.9659C99.5659 43.9659 99.6719 43.9593 99.7548 43.946C99.8377 43.9295 99.9039 43.9162 99.9537 43.9062L100.192 44.9602C100.113 44.9901 100.002 45.0199 99.8592 45.0497C99.7167 45.0829 99.536 45.0994 99.3173 45.0994C98.9859 45.0994 98.661 45.0282 98.3429 44.8857C98.028 44.7431 97.7662 44.526 97.5574 44.2344C97.3519 43.9427 97.2491 43.5748 97.2491 43.1307V35.5341ZM104.938 45.1591C104.202 45.1591 103.567 44.9967 103.034 44.6719C102.503 44.3438 102.094 43.8864 101.806 43.2997C101.521 42.7098 101.378 42.0237 101.378 41.2415C101.378 40.4593 101.521 39.7699 101.806 39.1733C102.094 38.5734 102.495 38.1061 103.009 37.7713C103.526 37.4332 104.129 37.2642 104.818 37.2642C105.216 37.2642 105.609 37.3305 105.997 37.4631C106.384 37.5956 106.737 37.8111 107.056 38.1094C107.374 38.4044 107.627 38.7955 107.816 39.2827C108.005 39.7699 108.1 40.3698 108.1 41.0824V41.5795H102.213V40.5653H106.906C106.906 40.1345 106.82 39.75 106.648 39.4119C106.479 39.0739 106.237 38.8071 105.922 38.6115C105.61 38.416 105.243 38.3182 104.818 38.3182C104.351 38.3182 103.947 38.4342 103.605 38.6662C103.267 38.8949 103.007 39.1932 102.825 39.5611C102.642 39.929 102.551 40.3234 102.551 40.7443V41.4205C102.551 41.9972 102.651 42.486 102.85 42.8871C103.052 43.2848 103.332 43.5881 103.69 43.7969C104.048 44.0024 104.464 44.1051 104.938 44.1051C105.246 44.1051 105.524 44.062 105.773 43.9759C106.025 43.8864 106.242 43.7538 106.424 43.5781C106.606 43.3991 106.747 43.1771 106.847 42.9119L107.98 43.2301C107.861 43.6146 107.66 43.9527 107.379 44.2443C107.097 44.5327 106.749 44.758 106.335 44.9205C105.92 45.0795 105.455 45.1591 104.938 45.1591ZM109.884 45V37.3636H111.018V38.517H111.097C111.237 38.1392 111.489 37.8326 111.853 37.5973C112.218 37.362 112.629 37.2443 113.086 37.2443C113.172 37.2443 113.28 37.246 113.409 37.2493C113.539 37.2526 113.636 37.2576 113.703 37.2642V38.4574C113.663 38.4474 113.572 38.4325 113.429 38.4126C113.29 38.3894 113.142 38.3778 112.987 38.3778C112.615 38.3778 112.284 38.4557 111.992 38.6115C111.704 38.764 111.475 38.9761 111.306 39.2479C111.141 39.5163 111.058 39.8229 111.058 40.1676V45H109.884Z"
        fill="#6C757D"
      />
    </svg>
  );
};

export const CrossIcon = (props: React.SVGAttributes<SVGElement>) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_208_64919)">
        <g clipPath="url(#clip1_208_64919)">
          <path
            d="M13.0699 14.0297L9.99991 10.8577L6.92891 14.0297C6.87845 14.0829 6.81742 14.1249 6.74973 14.1531C6.68205 14.1813 6.60921 14.1949 6.53592 14.1932C6.46263 14.1915 6.39051 14.1745 6.3242 14.1432C6.25789 14.112 6.19886 14.0672 6.15091 14.0117C6.05047 13.8957 5.99677 13.7465 6.00029 13.5931C6.0038 13.4396 6.06426 13.293 6.16991 13.1817L8.96991 10.2347C9.00508 10.1976 9.02468 10.1484 9.02468 10.0972C9.02468 10.0461 9.00508 9.99687 8.96991 9.95973L6.17091 7.01173C6.06478 6.90094 6.00388 6.75449 6.00018 6.60111C5.99648 6.44773 6.05024 6.2985 6.15091 6.18273C6.19873 6.12721 6.25762 6.08229 6.32381 6.05086C6.39 6.01943 6.46202 6.00217 6.53527 6.00019C6.60852 5.99821 6.68137 6.01156 6.74915 6.03938C6.81694 6.0672 6.87817 6.10887 6.92891 6.16173L9.99991 9.33573L13.0699 6.16173C13.1206 6.1089 13.1817 6.06724 13.2494 6.03943C13.3171 6.01162 13.3899 5.99828 13.4631 6.00025C13.5362 6.00223 13.6082 6.01949 13.6743 6.05091C13.7404 6.08234 13.7992 6.12724 13.8469 6.18273C13.9483 6.29806 14.0027 6.44723 13.9993 6.60073C13.996 6.75424 13.9352 6.90091 13.8289 7.01173L11.0289 9.95873C10.9937 9.99587 10.9742 10.0451 10.9742 10.0962C10.9742 10.1474 10.9937 10.1966 11.0289 10.2337L13.8309 13.1817C13.9366 13.2934 13.9969 13.4404 13.9998 13.5941C14.0028 13.7478 13.9483 13.8971 13.8469 14.0127C13.7991 14.0682 13.7401 14.1129 13.6739 14.1442C13.6077 14.1754 13.5356 14.1925 13.4624 14.1942C13.3892 14.1959 13.3164 14.1822 13.2488 14.154C13.1812 14.1259 13.1203 14.0839 13.0699 14.0307V14.0297Z"
            fill="#1F4A70"
          />
        </g>
      </g>
      <defs>
        <clipPath id="clip0_208_64919">
          <rect width="20" height="20" fill="white" />
        </clipPath>
        <clipPath id="clip1_208_64919">
          <rect
            width="8"
            height="8.194"
            fill="white"
            transform="translate(6 6)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
