import React, {forwardRef, useImperativeHandle} from 'react';
import GroupedCheckboxGrid from '../../components/GroupedCheckboxGrid';
import {TemplateForm} from '../../types/template';
import {useDataStoreContext} from '../../context';
import {generateGroupedOptions} from '../../utils/helper';
import {RiskForm} from '../../types';

export const AtRiskStep = forwardRef(
  (
    {
      form,
      setForm,
      onValidate,
      isEdit = false,
    }: {
      form: TemplateForm | RiskForm;
      setForm: any;
      onValidate?: (valid: boolean) => void;
      isEdit?: boolean;
    },
    ref,
  ) => {
    const {
      dataStore: {riskParameterType},
    } = useDataStoreContext();

    const groups = generateGroupedOptions(riskParameterType, 3);

    // Validation logic: at least one parameter must be selected
    const validate = () => {
      const params = form.parameters || [];
      // Valid if at least one group has a selected parameter or a non-empty "Others" value
      const valid =
        Array.isArray(params) &&
        params.some(
          p =>
            (Array.isArray(p.parameter_id) && p.parameter_id.length > 0) ||
            (p.is_other && p.value && p.value.trim().length > 0),
        );
      if (onValidate) onValidate(valid);
      return valid;
    };

    useImperativeHandle(ref, () => ({
      validate,
    }));

    // Validate on every relevant form change
    React.useEffect(() => {
      validate();
      // eslint-disable-next-line
    }, [form.parameters]);

    // Handler to update form.parameters
    const handleGroupedChange = (params: any[]) => {
      setForm((prev: TemplateForm) => ({
        ...prev,
        parameters: params,
      }));
    };

    return (
      <GroupedCheckboxGrid
        title={form?.task_requiring_ra || ''}
        subtitle="Give consideration to Groups of Individuals at Risk from the Hazards Identified"
        groups={groups}
        value={form.parameters || []}
        onChange={handleGroupedChange}
        othersPlaceholder="List the People at Risk"
        othersMaxLength={200}
        isEdit={isEdit}
        dateOfRiskAssessment={(form as RiskForm)?.date_risk_assessment ?? ''}
      />
    );
  },
);

AtRiskStep.displayName = 'AtRiskStep';
