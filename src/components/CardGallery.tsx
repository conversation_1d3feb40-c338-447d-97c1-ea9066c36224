import React, {useCallback, useEffect, useRef} from 'react';
import {Row, Spinner} from 'react-bootstrap';

import '../styles/components/card-gallery.scss';

interface CardGalleryProps<T> {
  data: T[];
  renderItem: (item: T) => React.ReactNode;
  isLoading?: boolean;
  isFetchingNextPage?: boolean;
  pagination?: {
    page: number;
    totalPages: number;
    pageSize: number;
  };
  fetchNextPage?: () => void;
}

function CardGallery<T>({
  data,
  renderItem,
  isLoading,
  isFetchingNextPage,
  pagination,
  fetchNextPage,
}: CardGalleryProps<T>) {
  const containerRef = useRef<HTMLDivElement>(null);

  const fetchMoreOnBottomReached = useCallback(
    (containerRefElement?: HTMLDivElement | null) => {
      if (containerRefElement && fetchNextPage && pagination) {
        const {scrollHeight, scrollTop, clientHeight} = containerRefElement;

        if (scrollHeight > clientHeight) {
          const scrolledToBottom =
            scrollHeight - scrollTop - clientHeight < 300;

          if (
            scrolledToBottom &&
            !isFetchingNextPage &&
            pagination.page < pagination.totalPages
          ) {
            fetchNextPage();
          }
        }
      }
    },
    [fetchNextPage, isFetchingNextPage, pagination],
  );

  useEffect(() => {
    if (fetchNextPage && !isFetchingNextPage) {
      const timer = setTimeout(() => {
        fetchMoreOnBottomReached(containerRef.current);
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [fetchMoreOnBottomReached, data, isFetchingNextPage, fetchNextPage]);

  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-item-center p-4">
        Loading...
      </div>
    );
  }

  if (!data?.length) {
    return (
      <div className="d-flex justify-content-center align-item-center p-4">
        No items found
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      onScroll={e => fetchMoreOnBottomReached(e.currentTarget)}
      className="card-gallery-container"
    >
      {data.map((item, index) => (
        <React.Fragment key={index}>{renderItem(item)}</React.Fragment>
      ))}
      {isFetchingNextPage && (
        <div className="w-100 d-flex justify-content-center align-item-center p-4">
          <Spinner animation="border" variant="primary" size="sm" />
        </div>
      )}
    </div>
  );
}

export default CardGallery;
