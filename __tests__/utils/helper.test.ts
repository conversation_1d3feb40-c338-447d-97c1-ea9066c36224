import {
  generateGroupedOptions,
  removeAndReindexJobState,
  formParameterHandler,
  createFormFromData,
} from '../../src/utils/helper';
import {Parameter} from '../../src/types/template';
import {TemplateFormStatus} from '../../src/enums';

describe('generateGroupedOptions', () => {
  it('should generate grouped options with default columns', () => {
    const input = [
      {
        id: 1,
        name: 'Group One',
        parameters: [
          {id: 10, name: 'Option A'},
          {id: 11, name: 'Option B'},
        ],
      },
      {
        id: 2,
        name: 'Group Two',
        parameters: [{id: 20, name: 'Option C'}],
      },
    ];

    const expected = [
      {
        id: 1,
        label: 'GROUP ONE',
        options: [
          {id: 10, label: 'Option A'},
          {id: 11, label: 'Option B'},
        ],
        columns: 3,
      },
      {
        id: 2,
        label: 'GROUP TWO',
        options: [{id: 20, label: 'Option C'}],
        columns: 3,
      },
    ];

    expect(generateGroupedOptions(input)).toEqual(expected);
  });

  it('should use custom columns when provided', () => {
    const input = [
      {
        id: 5,
        name: 'Custom Group',
        parameters: [{id: 50, name: 'Option X'}],
      },
    ];

    const expected = [
      {
        id: 5,
        label: 'CUSTOM GROUP',
        options: [{id: 50, label: 'Option X'}],
        columns: 5,
      },
    ];

    expect(generateGroupedOptions(input, 5)).toEqual(expected);
  });

  it('should handle empty input array', () => {
    expect(generateGroupedOptions([])).toEqual([]);
  });

  it('should handle group with empty parameters array', () => {
    const input = [
      {
        id: 3,
        name: 'Empty Params',
        parameters: [],
      },
    ];

    const expected = [
      {
        id: 3,
        label: 'EMPTY PARAMS',
        options: [],
        columns: 3,
      },
    ];

    expect(generateGroupedOptions(input)).toEqual(expected);
  });

  it('should handle group names with special characters', () => {
    const input = [
      {
        id: 1,
        name: 'group-with_special.chars',
        parameters: [{id: 1, name: 'Option 1'}],
      },
    ];

    const expected = [
      {
        id: 1,
        label: 'GROUP-WITH_SPECIAL.CHARS',
        options: [{id: 1, label: 'Option 1'}],
        columns: 3,
      },
    ];

    expect(generateGroupedOptions(input)).toEqual(expected);
  });

  it('should handle zero columns', () => {
    const input = [
      {
        id: 1,
        name: 'Test Group',
        parameters: [{id: 1, name: 'Option 1'}],
      },
    ];

    const expected = [
      {
        id: 1,
        label: 'TEST GROUP',
        options: [{id: 1, label: 'Option 1'}],
        columns: 0,
      },
    ];

    expect(generateGroupedOptions(input, 0)).toEqual(expected);
  });
});

describe('removeAndReindexJobState', () => {
  it('should remove item at index and reindex remaining items', () => {
    const state = {
      0: {name: 'Job 0'},
      1: {name: 'Job 1'},
      2: {name: 'Job 2'},
      3: {name: 'Job 3'},
    };

    const result = removeAndReindexJobState(state, 1);

    expect(result).toEqual({
      0: {name: 'Job 0'},
      1: {name: 'Job 2'},
      2: {name: 'Job 3'},
    });
  });

  it('should remove first item and reindex correctly', () => {
    const state = {
      0: {name: 'Job 0'},
      1: {name: 'Job 1'},
      2: {name: 'Job 2'},
    };

    const result = removeAndReindexJobState(state, 0);

    expect(result).toEqual({
      0: {name: 'Job 1'},
      1: {name: 'Job 2'},
    });
  });

  it('should remove last item and keep other indices unchanged', () => {
    const state = {
      0: {name: 'Job 0'},
      1: {name: 'Job 1'},
      2: {name: 'Job 2'},
    };

    const result = removeAndReindexJobState(state, 2);

    expect(result).toEqual({
      0: {name: 'Job 0'},
      1: {name: 'Job 1'},
    });
  });

  it('should handle empty state object', () => {
    const state = {};
    const result = removeAndReindexJobState(state, 0);
    expect(result).toEqual({});
  });

  it('should handle single item removal', () => {
    const state = {0: {name: 'Only Job'}};
    const result = removeAndReindexJobState(state, 0);
    expect(result).toEqual({});
  });

  it('should handle non-existent index gracefully', () => {
    const state = {
      0: {name: 'Job 0'},
      1: {name: 'Job 1'},
    };

    const result = removeAndReindexJobState(state, 5);

    expect(result).toEqual({
      0: {name: 'Job 0'},
      1: {name: 'Job 1'},
    });
  });

  it('should handle non-sequential indices', () => {
    const state = {
      0: {name: 'Job 0'},
      2: {name: 'Job 2'},
      5: {name: 'Job 5'},
    };

    const result = removeAndReindexJobState(state, 2);

    expect(result).toEqual({
      0: {name: 'Job 0'},
      4: {name: 'Job 5'}, // Index 5 becomes 4 (5-1) since it's greater than removed index 2
    });
  });

  it('should preserve object properties and structure', () => {
    const state = {
      0: {
        id: 1,
        name: 'Job 0',
        status: 'active',
        metadata: {created: '2023-01-01'},
      },
      1: {
        id: 2,
        name: 'Job 1',
        status: 'pending',
        metadata: {created: '2023-01-02'},
      },
      2: {
        id: 3,
        name: 'Job 2',
        status: 'completed',
        metadata: {created: '2023-01-03'},
      },
    };

    const result = removeAndReindexJobState(state, 1);

    expect(result).toEqual({
      0: {
        id: 1,
        name: 'Job 0',
        status: 'active',
        metadata: {created: '2023-01-01'},
      },
      1: {
        id: 3,
        name: 'Job 2',
        status: 'completed',
        metadata: {created: '2023-01-03'},
      },
    });
  });
});

describe('formParameterHandler', () => {
  it('should delete template_hazard.value when template_hazard[0].isOther is false and value is empty', () => {
    const payload = {
      template_hazard: [
        {
          isOther: false,
        },
      ] as any,
      parameters: [],
    };
    payload.template_hazard.value = '';

    const result = formParameterHandler(payload);

    expect(result.template_hazard).not.toHaveProperty('value');
  });

  it('should keep template_hazard.value when template_hazard[0].isOther is true', () => {
    const payload = {
      template_hazard: [
        {
          isOther: true,
        },
      ] as any,
      parameters: [],
    };
    payload.template_hazard.value = '';

    const result = formParameterHandler(payload);

    expect(result.template_hazard.value).toBe('');
  });

  it('should keep template_hazard.value when value is not empty', () => {
    const payload = {
      template_hazard: [
        {
          isOther: false,
        },
      ] as any,
      parameters: [],
    };
    payload.template_hazard.value = 'some value';

    const result = formParameterHandler(payload);

    expect(result.template_hazard.value).toBe('some value');
  });

  it('should filter parameters based on parameter_id length and is_other flag', () => {
    const parameters: Parameter[] = [
      {
        is_other: false,
        parameter_type_id: 1,
        parameter_id: [1, 2],
        value: 'test',
      },
      {
        is_other: false,
        parameter_type_id: 2,
        parameter_id: [],
        value: 'empty array',
      },
      {
        is_other: true,
        parameter_type_id: 3,
        parameter_id: [],
        value: 'other param',
      },
    ];

    const payload = {
      parameters,
      template_hazard: [{isOther: true}] as any,
    };
    payload.template_hazard.value = 'test';

    const result = formParameterHandler(payload);

    expect(result.parameters).toHaveLength(2);
    expect(result.parameters[0].parameter_id).toEqual([1, 2]);
    expect(result.parameters[1].is_other).toBe(true);
  });

  it('should handle undefined parameters array', () => {
    const payload = {
      parameters: undefined,
      template_hazard: [{isOther: true}] as any,
    };
    payload.template_hazard.value = 'test';

    const result = formParameterHandler(payload);

    expect(result.parameters).toBeUndefined();
  });

  it('should process template_job array and remove job_id when array exists', () => {
    const payload = {
      template_job: [
        {
          job_id: 'id1',
          name: 'Job 1',
          template_job_residual_risk_rating: [
            {rating: 5, parameter_type_id: 1, reason: 'test reason'},
            {rating: 3, parameter_type_id: 2},
          ],
          template_job_initial_risk_rating: [{rating: 4, parameter_type_id: 1}],
        },
        {
          job_id: 'id2',
          name: 'Job 2',
          template_job_residual_risk_rating: [],
          template_job_initial_risk_rating: [],
        },
      ],
      parameters: [],
    };

    const result = formParameterHandler(payload);

    expect(result.template_job[0]).not.toHaveProperty('job_id');
    expect(result.template_job[1]).not.toHaveProperty('job_id');
    expect(result.template_job[0].name).toBe('Job 1');
    expect(result.template_job[1].name).toBe('Job 2');

    // Check risk rating processing
    expect(result.template_job[0].template_job_residual_risk_rating).toEqual([
      {rating: 5, parameter_type_id: 1, reason: 'test reason'},
      {rating: 3, parameter_type_id: 2},
    ]);
    expect(result.template_job[0].template_job_initial_risk_rating).toEqual([
      {rating: 4, parameter_type_id: 1},
    ]);
  });

  it('should not modify template_job when it is not an array', () => {
    const payload = {
      template_job: {job_id: 'id1', name: 'Job 1'},
      parameters: [],
    };

    const result = formParameterHandler(payload);

    expect(result.template_job.job_id).toBe('id1');
    expect(result.template_job.name).toBe('Job 1');
  });

  it('should process template_task_reliability_assessment when array has length', () => {
    const payload = {
      template_task_reliability_assessment: [
        {
          condition: 'test condition',
          task_reliability_assessment_answer: 'test answer',
          task_reliability_assessment_id: 1,
        },
        {
          condition: '',
          task_reliability_assessment_answer: 'empty condition answer',
          id: 2,
        },
        {
          condition: 'another condition',
          task_reliability_assessmen: 'typo property answer',
          task_reliability_assessment_id: 3,
        },
      ],
      parameters: [],
    };

    const result = formParameterHandler(payload);

    expect(result.template_task_reliability_assessment).toEqual([
      {
        condition: 'test condition',
        task_reliability_assessment_answer: 'test answer',
        task_reliability_assessment_id: 1,
      },
      {
        task_reliability_assessment_answer: 'empty condition answer',
        task_reliability_assessment_id: 2,
      }, // condition removed because it's empty
      {
        condition: 'another condition',
        task_reliability_assessment_answer: 'typo property answer',
        task_reliability_assessment_id: 3,
      },
    ]);
  });

  it('should handle template_task_reliability_assessment with missing properties', () => {
    const payload = {
      template_task_reliability_assessment: [
        {condition: 'keep condition'},
        {condition: ''},
        {task_reliability_assessment_answer: 'existing answer'},
      ],
      parameters: [],
    };

    const result = formParameterHandler(payload);

    expect(result.template_task_reliability_assessment).toEqual([
      {
        condition: 'keep condition',
        task_reliability_assessment_answer: '',
        task_reliability_assessment_id: null,
      },
      {
        task_reliability_assessment_answer: '',
        task_reliability_assessment_id: null,
      },
      {
        task_reliability_assessment_answer: 'existing answer',
        task_reliability_assessment_id: null,
      },
    ]);
  });

  it('should remove value property from parameters when is_other is false', () => {
    const parameters: Parameter[] = [
      {
        is_other: false,
        parameter_type_id: 1,
        parameter_id: [1],
        value: 'should be removed',
      },
      {
        is_other: true,
        parameter_type_id: 2,
        parameter_id: [2],
        value: 'should be kept',
      },
    ];

    const payload = {
      parameters,
      template_hazard: [{isOther: true}] as any,
    };
    payload.template_hazard.value = 'test';

    const result = formParameterHandler(payload);

    expect(result.parameters[0]).not.toHaveProperty('value');
    expect(result.parameters[0].parameter_type_id).toBe(1);
    expect(result.parameters[1].value).toBe('should be kept');
  });

  it('should handle complex payload with all features', () => {
    const parameters: Parameter[] = [
      {
        is_other: false,
        parameter_type_id: 1,
        parameter_id: [1, 2],
        value: 'remove this value',
      },
      {
        is_other: true,
        parameter_type_id: 2,
        parameter_id: [],
        value: 'keep this value',
      },
      {
        is_other: false,
        parameter_type_id: 3,
        parameter_id: [],
        value: 'filter this out',
      },
    ];

    const payload = {
      template_hazard: [
        {
          isOther: false,
        },
      ] as any,
      parameters,
      template_job: [{job_id: 'remove_me', name: 'Job 1', step: 'Step 1'}],
      template_task_reliability_assessment: [
        {
          condition: 'keep condition',
          task_reliability_assessment_answer: 'answer 1',
        },
        {condition: '', task_reliability_assessment_answer: 'answer 2'},
      ],
    };
    payload.template_hazard.value = '';

    const result = formParameterHandler(payload);

    // template_hazard.value should be deleted
    expect(result.template_hazard).not.toHaveProperty('value');

    // Only 2 parameters should remain (first has parameter_id, second is_other)
    expect(result.parameters).toHaveLength(2);
    expect(result.parameters[0]).not.toHaveProperty('value');
    expect(result.parameters[1].value).toBe('keep this value');

    // job_id should be removed from template_job
    expect(result.template_job[0]).not.toHaveProperty('job_id');
    expect(result.template_job[0].name).toBe('Job 1');

    // template_task_reliability_assessment should be processed
    expect(result.template_task_reliability_assessment).toEqual([
      {
        condition: 'keep condition',
        task_reliability_assessment_answer: 'answer 1',
        task_reliability_assessment_id: null,
      },
      {
        task_reliability_assessment_answer: 'answer 2',
        task_reliability_assessment_id: null,
      },
    ]);
  });

  it('should handle null and undefined values gracefully', () => {
    const payload = {
      template_hazard: null,
      parameters: null,
      job: null,
      template_job: null,
      template_task_reliability_assessment: null,
    };

    const result = formParameterHandler(payload);

    expect(result.template_hazard).toBeNull();
    expect(result.parameters).toBeUndefined(); // filter on null returns undefined
  });

  it('should handle empty arrays', () => {
    const payload = {
      parameters: [],
      template_hazard: [{isOther: false}] as any,
      job: [],
      template_job: [],
      template_task_reliability_assessment: [],
    };
    payload.template_hazard.value = '';

    const result = formParameterHandler(payload);

    expect(result.parameters).toEqual([]);
    expect(result.template_job).toEqual([]);
    expect(result.template_task_reliability_assessment).toEqual([]);
  });

  it('should handle parameters with null values', () => {
    const parameters: Parameter[] = [
      {
        is_other: false,
        parameter_type_id: 1,
        parameter_id: [1],
        value: 'test',
      },
      null as any, // null parameter
      {
        is_other: true,
        parameter_type_id: 2,
        parameter_id: [],
        value: 'keep this',
      },
    ];

    const payload = {
      parameters,
      template_hazard: [{isOther: true}] as any,
    };
    payload.template_hazard.value = 'test';

    const result = formParameterHandler(payload);

    // Should filter out null parameter and process others
    expect(result.parameters).toHaveLength(2);
    expect(result.parameters[0]).not.toHaveProperty('value');
    expect(result.parameters[1].value).toBe('keep this');
  });

  it('should handle template_hazard without array structure', () => {
    const payload = {
      template_hazard: {
        isOther: false,
        value: '',
      },
      parameters: [],
    };

    const result = formParameterHandler(payload);

    // Should delete value because template_hazard[0] is undefined (falsy) and value is empty
    expect(result.template_hazard).not.toHaveProperty('value');
  });

  it('should handle undefined template_job gracefully', () => {
    const payload = {
      template_job: undefined,
      parameters: [],
    };

    const result = formParameterHandler(payload);

    expect(result.template_job).toBeUndefined();
  });

  it('should handle template_job with risk ratings correctly', () => {
    const payload = {
      template_job: [
        {
          job_id: 'test',
          template_job_residual_risk_rating: [
            {rating: 5, parameter_type_id: 1, reason: 'high risk'},
            {rating: 3, parameter_type_id: 2}, // no reason
          ],
          template_job_initial_risk_rating: [
            {rating: 4, parameter_type_id: 1, extra_prop: 'should be removed'},
          ],
        },
      ],
      parameters: [],
    };

    const result = formParameterHandler(payload);

    expect(result.template_job[0].template_job_residual_risk_rating).toEqual([
      {rating: 5, parameter_type_id: 1, reason: 'high risk'},
      {rating: 3, parameter_type_id: 2},
    ]);
    expect(result.template_job[0].template_job_initial_risk_rating).toEqual([
      {rating: 4, parameter_type_id: 1},
    ]);
  });
});

describe('createFormFromData', () => {
  it('should create form with default values when no data provided', () => {
    const result = createFormFromData();

    expect(result).toEqual({
      task_requiring_ra: '',
      task_duration: '',
      task_alternative_consideration: '',
      task_rejection_reason: '',
      worst_case_scenario: '',
      recovery_measures: '',
      status: TemplateFormStatus.DRAFT,
      parameters: [],
      template_category: {
        category_id: [],
      },
      template_job: [
        {
          job_id: expect.any(String),
          job_step: '',
          job_hazard: '',
          job_nature_of_risk: '',
          job_existing_control: '',
          job_additional_mitigation: '',
          job_close_out_date: '2025-05-21',
          job_close_out_responsibility_id: '1',
          template_job_initial_risk_rating: [],
          template_job_residual_risk_rating: [],
        },
      ],
      template_hazard: {
        is_other: false,
        value: '',
        hazard_id: [],
      },
      template_task_reliability_assessment: [],
      template_keyword: ['test'],
    });
  });

  it('should create form with provided data values', () => {
    const inputData = {
      task_requiring_ra: 'Test task',
      task_duration: '2 hours',
      task_alternative_consideration: 'Alternative approach',
      task_rejection_reason: 'Not applicable',
      worst_case_scenario: 'System failure',
      recovery_measures: 'Backup system',
    };

    const result = createFormFromData(inputData);

    expect(result.task_requiring_ra).toBe('Test task');
    expect(result.task_duration).toBe('2 hours');
    expect(result.task_alternative_consideration).toBe('Alternative approach');
    expect(result.task_rejection_reason).toBe('Not applicable');
    expect(result.worst_case_scenario).toBe('System failure');
    expect(result.recovery_measures).toBe('Backup system');
    expect(result.status).toBe(TemplateFormStatus.DRAFT);
  });

  it('should handle empty template_category array', () => {
    const inputData = {
      template_category: [],
    };

    const result = createFormFromData(inputData);

    expect(result.template_category).toEqual({
      category_id: [],
    });
  });

  it('should map template_hazards correctly', () => {
    const inputData = {
      template_hazards: [
        {
          hazard_category_is_other: false,
          hazard_detail: {id: 1},
        },
        {
          hazard_category_is_other: false,
          hazard_detail: {id: 2},
        },
        {
          hazard_category_is_other: true,
          value: 'Custom hazard',
        },
      ],
    };

    const result = createFormFromData(inputData);

    expect(result.template_hazard).toEqual({
      is_other: true,
      value: 'Custom hazard',
      hazard_id: [1, 2],
    });
  });

  it('should handle template_hazards without other values', () => {
    const inputData = {
      template_hazards: [
        {
          hazard_category_is_other: false,
          hazard_detail: {id: 5},
        },
      ],
    };

    const result = createFormFromData(inputData);

    expect(result.template_hazard).toEqual({
      is_other: false,
      value: '',
      hazard_id: [5],
    });
  });
});
