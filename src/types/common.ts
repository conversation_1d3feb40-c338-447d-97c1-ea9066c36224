export type CommonOptionResponse = {
  message: string;
  result: {
    id: number;
    name: string;
  }[];
};

export type CommonStringOptionResponse = {
  message: string;
  result: string[];
};

export interface OfficeItem {
  id: number;
  value: string;
  ship_party_id: number;
}

export interface CrewMember {
  seafarer_id: number;
  seafarer_person_id: number;
  seafarer_hkid: number;
  seafarer_rank_id: number;
  seafarer_name: string;
  seafarer_rank: string;
  seafarer_rank_sort_order: number;
}
