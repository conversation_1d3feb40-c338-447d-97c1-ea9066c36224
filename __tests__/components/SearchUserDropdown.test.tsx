import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import SearchUserDropdown from '../../src/components/SearchUserDropdown';

// Mock the dependencies
jest.mock('../../src/utils/svgIcons', () => ({
  CheckFilled: () => <svg data-testid="check-filled" />,
  CheckUnFilled: () => <svg data-testid="check-unfilled" />,
}));

jest.mock('../../src/components/SingleBadgePopover', () => {
  return function MockSingleBadgePopover({
    label,
  }: {
    items: string[];
    label: string;
  }) {
    return <span data-testid="single-badge-popover">{label}</span>;
  };
});

jest.mock('../../src/components/SearchInput', () => {
  return function MockSearchInput({
    placeholder,
    value,
    onSearch,
  }: {
    placeholder?: string;
    value: string;
    onSearch: (query: string | null) => void;
  }) {
    return (
      <input
        data-testid="search-input"
        placeholder={placeholder}
        value={value}
        onChange={e => onSearch(e.target.value || null)}
      />
    );
  };
});

jest.mock('../../src/components/CheckboxComponent', () => {
  return function MockCheckboxComponent({
    checked,
    onChange,
    id,
  }: {
    checked: boolean;
    onChange: () => void;
    id: string;
  }) {
    return (
      <input
        data-testid={`checkbox-${id}`}
        type="checkbox"
        checked={checked}
        onChange={onChange}
      />
    );
  };
});

jest.mock('react-bootstrap-icons', () => ({
  ChevronDown: () => <svg data-testid="chevron-down" />,
}));

jest.mock('react-bootstrap', () => ({
  Form: {
    Group: ({children}: any) => <div>{children}</div>,
    Control: (props: any) => <input {...props} />,
  },
  OverlayTrigger: ({children}: any) => <div>{children}</div>,
  Popover: {
    Body: ({children}: any) => <div>{children}</div>,
  },
}));

jest.mock('react-icons/io', () => ({
  IoIosSearch: () => <svg data-testid="search-icon" />,
}));

const mockUsers = [
  {
    id: '1',
    full_name: 'Alice Johnson',
    designation: 'Developer',
    email: '<EMAIL>',
  },
  {
    id: '2',
    full_name: 'Bob Smith',
    designation: 'Designer',
    email: '<EMAIL>',
  },
  {
    id: '3',
    full_name: 'Charlie Lee',
    designation: 'QA',
    email: '<EMAIL>',
  },
  {
    id: '4',
    full_name: 'David Wilson',
    email: '<EMAIL>',
    // No designation to test optional field
  },
  {
    id: '5',
    full_name: 'Eve',
    designation: 'Manager',
    email: '<EMAIL>',
  },
];

describe('SearchUserDropdown', () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with placeholder when no users are selected', () => {
    render(
      <SearchUserDropdown
        value={[]}
        options={mockUsers}
        onChange={mockOnChange}
      />,
    );

    expect(screen.getByText('Select users')).toBeInTheDocument();
  });

  it('opens dropdown on click and displays all users', async () => {
    render(
      <SearchUserDropdown
        value={[]}
        options={mockUsers}
        onChange={mockOnChange}
      />,
    );

    // Click the main input to open dropdown
    const mainInput = screen.getByRole('combobox');
    fireEvent.click(mainInput);

    await waitFor(() => {
      expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
      expect(screen.getByText('Bob Smith')).toBeInTheDocument();
      expect(screen.getByText('Charlie Lee')).toBeInTheDocument();
    });
  });

  it('filters users based on search input', async () => {
    render(
      <SearchUserDropdown
        value={[]}
        options={mockUsers}
        onChange={mockOnChange}
      />,
    );

    // Open dropdown first
    const mainInput = screen.getByRole('combobox');
    fireEvent.click(mainInput);

    // Wait for dropdown to open and find search input
    await waitFor(() => {
      expect(screen.getByPlaceholderText('Search')).toBeInTheDocument();
    });

    const searchInput = screen.getByPlaceholderText('Search');
    fireEvent.change(searchInput, {
      target: {value: 'Charlie'},
    });

    await waitFor(() => {
      expect(screen.getByText('Charlie Lee')).toBeInTheDocument();
      expect(screen.queryByText('Alice Johnson')).not.toBeInTheDocument();
    });
  });

  it('calls onChange when a user is selected', async () => {
    render(
      <SearchUserDropdown
        value={[]}
        options={mockUsers}
        onChange={mockOnChange}
      />,
    );

    // Open dropdown first
    const mainInput = screen.getByRole('combobox');
    fireEvent.click(mainInput);

    await waitFor(() => {
      expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
    });

    // Find and click the user item (it's a div containing the user name)
    const aliceButton = Array.from(
      document.querySelectorAll('.user-list-item'),
    ).find(button => button.textContent?.includes('Alice Johnson'));
    expect(aliceButton).toBeDefined();
    fireEvent.click(aliceButton!);

    // The component calls onChange with the selected user IDs (strings)
    await waitFor(() => {
      expect(mockOnChange).toHaveBeenCalledWith(['1']);
    });
  });

  it('closes dropdown when clicking outside', async () => {
    render(
      <div>
        <SearchUserDropdown
          value={[]}
          options={mockUsers}
          onChange={mockOnChange}
        />
        <div data-testid="outside">Outside</div>
      </div>,
    );

    // Open dropdown first
    const mainInput = screen.getByRole('combobox');
    fireEvent.click(mainInput);

    await waitFor(() => {
      expect(screen.getByText('Select all')).toBeInTheDocument();
    });

    // Click outside to close dropdown
    fireEvent.mouseDown(screen.getByTestId('outside'));

    await waitFor(() => {
      expect(screen.queryByText('Select all')).not.toBeInTheDocument();
    });
  });

  // Additional test cases for increased coverage
  describe('Custom placeholder', () => {
    it('renders with custom placeholder', () => {
      render(
        <SearchUserDropdown
          value={[]}
          options={mockUsers}
          onChange={mockOnChange}
          placeholder="Choose team members"
        />,
      );

      expect(screen.getByText('Choose team members')).toBeInTheDocument();
    });
  });

  describe('Selected users display', () => {
    it('displays selected user names', () => {
      render(
        <SearchUserDropdown
          value={['1', '2']}
          options={mockUsers}
          onChange={mockOnChange}
        />,
      );

      // The component shows only the first name when multiple are selected due to layout constraints
      expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
    });

    it('displays counter when more than maxDisplayNames are selected', () => {
      render(
        <SearchUserDropdown
          value={['1', '2', '3']}
          options={mockUsers}
          onChange={mockOnChange}
          maxDisplayNames={2}
        />,
      );

      // Should show first name and a counter for additional users
      expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
      expect(screen.getByTestId('single-badge-popover')).toBeInTheDocument();
      expect(screen.getByText('+2 More')).toBeInTheDocument();
    });

    it('shows tooltip with all selected user names', () => {
      render(
        <SearchUserDropdown
          value={['1', '2', '3']}
          options={mockUsers}
          onChange={mockOnChange}
        />,
      );

      const namesSpan = screen.getByTitle(
        'Alice Johnson, Bob Smith, Charlie Lee',
      );
      expect(namesSpan).toBeInTheDocument();
    });
  });

  describe('User deselection', () => {
    it('deselects a user when clicked again', async () => {
      render(
        <SearchUserDropdown
          value={['1']}
          options={mockUsers}
          onChange={mockOnChange}
        />,
      );

      // Open dropdown
      const mainInput = screen.getByRole('combobox');
      fireEvent.click(mainInput);

      await waitFor(() => {
        expect(screen.getByText('Select all')).toBeInTheDocument();
      });

      // Click Alice's checkbox to deselect
      const aliceCheckbox = screen.getByTestId('checkbox-user-check-1');
      fireEvent.click(aliceCheckbox);

      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalledWith([]);
      });
    });
  });

  describe('Select all functionality', () => {
    it.skip('deselects all when all are already selected', async () => {
      render(
        <SearchUserDropdown
          value={['1', '2', '3', '4', '5']}
          options={mockUsers}
          onChange={mockOnChange}
        />,
      );

      // Open dropdown
      const mainInput = screen.getByRole('combobox');
      fireEvent.click(mainInput);

      await waitFor(() => {
        expect(screen.getByText('Select all')).toBeInTheDocument();
      });

      const selectAllButton = screen.getByText('Select all');
      fireEvent.click(selectAllButton);

      // Should deselect all users
      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalledWith([]);
      });
    });

    it('selects only filtered users when search is active', async () => {
      render(
        <SearchUserDropdown
          value={[]}
          options={mockUsers}
          onChange={mockOnChange}
        />,
      );

      // Open dropdown
      const mainInput = screen.getByRole('combobox');
      fireEvent.click(mainInput);

      await waitFor(() => {
        expect(screen.getByTestId('search-input')).toBeInTheDocument();
      });

      // Search for users with 'Charlie' in name to get specific results
      const searchInput = screen.getByTestId('search-input');
      fireEvent.change(searchInput, {target: {value: 'Charlie'}});

      await waitFor(() => {
        expect(screen.getByText('Charlie Lee')).toBeInTheDocument();
        expect(screen.queryByText('Alice Johnson')).not.toBeInTheDocument();
      });

      // Click select all - should only select filtered users
      const selectAllButton = screen.getByText('Select all');
      fireEvent.click(selectAllButton);

      await waitFor(() => {
        // Should select only Charlie Lee who matches the filter
        expect(mockOnChange).toHaveBeenCalledWith(['3']);
      });
    });
  });

  describe('Search functionality', () => {
    it('filters users by email', async () => {
      render(
        <SearchUserDropdown
          value={[]}
          options={mockUsers}
          onChange={mockOnChange}
        />,
      );

      // Open dropdown
      const mainInput = screen.getByRole('combobox');
      fireEvent.click(mainInput);

      await waitFor(() => {
        expect(screen.getByTestId('search-input')).toBeInTheDocument();
      });

      // Search by email
      const searchInput = screen.getByTestId('search-input');
      fireEvent.change(searchInput, {target: {value: '<EMAIL>'}});

      await waitFor(() => {
        expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
        expect(screen.queryByText('Bob Smith')).not.toBeInTheDocument();
      });
    });

    it('filters users by designation', async () => {
      render(
        <SearchUserDropdown
          value={[]}
          options={mockUsers}
          onChange={mockOnChange}
        />,
      );

      // Open dropdown
      const mainInput = screen.getByRole('combobox');
      fireEvent.click(mainInput);

      await waitFor(() => {
        expect(screen.getByTestId('search-input')).toBeInTheDocument();
      });

      // Search by designation
      const searchInput = screen.getByTestId('search-input');
      fireEvent.change(searchInput, {target: {value: 'Developer'}});

      await waitFor(() => {
        expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
        expect(screen.queryByText('Bob Smith')).not.toBeInTheDocument();
      });
    });

    it('shows "No users found" when search returns no results', async () => {
      render(
        <SearchUserDropdown
          value={[]}
          options={mockUsers}
          onChange={mockOnChange}
        />,
      );

      // Open dropdown
      const mainInput = screen.getByRole('combobox');
      fireEvent.click(mainInput);

      await waitFor(() => {
        expect(screen.getByTestId('search-input')).toBeInTheDocument();
      });

      // Search for non-existent user
      const searchInput = screen.getByTestId('search-input');
      fireEvent.change(searchInput, {target: {value: 'nonexistent'}});

      await waitFor(() => {
        expect(screen.getByText('No users found.')).toBeInTheDocument();
      });
    });

    it('handles case-insensitive search', async () => {
      render(
        <SearchUserDropdown
          value={[]}
          options={mockUsers}
          onChange={mockOnChange}
        />,
      );

      // Open dropdown
      const mainInput = screen.getByRole('combobox');
      fireEvent.click(mainInput);

      await waitFor(() => {
        expect(screen.getByTestId('search-input')).toBeInTheDocument();
      });

      // Search with different case
      const searchInput = screen.getByTestId('search-input');
      fireEvent.change(searchInput, {target: {value: 'ALICE'}});

      await waitFor(() => {
        expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
      });
    });
  });

  describe('Keyboard accessibility', () => {
    it('opens dropdown on Enter key', () => {
      render(
        <SearchUserDropdown
          value={[]}
          options={mockUsers}
          onChange={mockOnChange}
        />,
      );

      const mainInput = screen.getByRole('combobox');
      fireEvent.keyDown(mainInput, {key: 'Enter'});

      expect(screen.getByText('Select all')).toBeInTheDocument();
    });

    it('opens dropdown on Space key', () => {
      render(
        <SearchUserDropdown
          value={[]}
          options={mockUsers}
          onChange={mockOnChange}
        />,
      );

      const mainInput = screen.getByRole('combobox');
      fireEvent.keyDown(mainInput, {key: ' '});

      expect(screen.getByText('Select all')).toBeInTheDocument();
    });

    it('closes dropdown on Escape key', async () => {
      render(
        <SearchUserDropdown
          value={[]}
          options={mockUsers}
          onChange={mockOnChange}
        />,
      );

      const mainInput = screen.getByRole('combobox');
      fireEvent.click(mainInput);

      await waitFor(() => {
        expect(screen.getByText('Select all')).toBeInTheDocument();
      });

      fireEvent.keyDown(mainInput, {key: 'Escape'});

      await waitFor(() => {
        expect(screen.queryByText('Select all')).not.toBeInTheDocument();
      });
    });
  });

  describe('User initials generation', () => {
    it('displays correct initials for users', async () => {
      render(
        <SearchUserDropdown
          value={[]}
          options={mockUsers}
          onChange={mockOnChange}
        />,
      );

      // Open dropdown
      const mainInput = screen.getByRole('combobox');
      fireEvent.click(mainInput);

      await waitFor(() => {
        // Check that avatars are rendered (they contain initials)
        expect(screen.getByText('AJ')).toBeInTheDocument(); // Alice Johnson
        expect(screen.getByText('BS')).toBeInTheDocument(); // Bob Smith
        expect(screen.getByText('CL')).toBeInTheDocument(); // Charlie Lee
        expect(screen.getByText('DW')).toBeInTheDocument(); // David Wilson
        expect(screen.getByText('EV')).toBeInTheDocument(); // Eve (single name)
      });
    });
  });

  describe('Edge cases', () => {
    it('handles empty options array', () => {
      render(
        <SearchUserDropdown value={[]} options={[]} onChange={mockOnChange} />,
      );

      const mainInput = screen.getByRole('combobox');
      fireEvent.click(mainInput);

      expect(screen.getByText('No users found.')).toBeInTheDocument();
    });

    it('handles users without designation', async () => {
      const usersWithoutDesignation = [
        {
          id: '1',
          full_name: 'John Doe',
          email: '<EMAIL>',
        },
      ];

      render(
        <SearchUserDropdown
          value={[]}
          options={usersWithoutDesignation}
          onChange={mockOnChange}
        />,
      );

      const mainInput = screen.getByRole('combobox');
      fireEvent.click(mainInput);

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });
    });

    it('handles invalid user IDs in value prop', () => {
      render(
        <SearchUserDropdown
          value={['invalid-id', '1']}
          options={mockUsers}
          onChange={mockOnChange}
        />,
      );

      // Should only show valid selected users
      expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
    });

    it('handles undefined/null search values', async () => {
      render(
        <SearchUserDropdown
          value={[]}
          options={mockUsers}
          onChange={mockOnChange}
        />,
      );

      const mainInput = screen.getByRole('combobox');
      fireEvent.click(mainInput);

      await waitFor(() => {
        expect(screen.getByTestId('search-input')).toBeInTheDocument();
      });

      // Test with null value
      const searchInput = screen.getByTestId('search-input');
      fireEvent.change(searchInput, {target: {value: null}});

      // Should show all users when search is null/empty
      await waitFor(() => {
        expect(screen.getByText('Alice Johnson')).toBeInTheDocument();
        expect(screen.getByText('Bob Smith')).toBeInTheDocument();
      });
    });
  });

  describe('Component lifecycle', () => {
    it('does not call onChange when component mounts with selected users', () => {
      render(
        <SearchUserDropdown
          value={['1', '2']}
          options={mockUsers}
          onChange={mockOnChange}
        />,
      );

      // Component should not call onChange on mount - it only calls onChange when user interacts
      expect(mockOnChange).not.toHaveBeenCalled();
    });

    it('handles window resize events', () => {
      const originalAddEventListener = window.addEventListener;
      const originalRemoveEventListener = window.removeEventListener;
      const addEventListenerSpy = jest.fn();
      const removeEventListenerSpy = jest.fn();

      window.addEventListener = addEventListenerSpy;
      window.removeEventListener = removeEventListenerSpy;

      const {unmount} = render(
        <SearchUserDropdown
          value={[]}
          options={mockUsers}
          onChange={mockOnChange}
        />,
      );

      expect(addEventListenerSpy).toHaveBeenCalledWith(
        'resize',
        expect.any(Function),
      );

      unmount();

      expect(removeEventListenerSpy).toHaveBeenCalledWith(
        'resize',
        expect.any(Function),
      );

      // Restore original methods
      window.addEventListener = originalAddEventListener;
      window.removeEventListener = originalRemoveEventListener;
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes', () => {
      render(
        <SearchUserDropdown
          value={[]}
          options={mockUsers}
          onChange={mockOnChange}
        />,
      );

      const mainInput = screen.getByRole('combobox');
      expect(mainInput).toHaveAttribute('aria-haspopup', 'listbox');
      expect(mainInput).toHaveAttribute('aria-expanded', 'false');
    });

    it('updates aria-expanded when dropdown opens', () => {
      render(
        <SearchUserDropdown
          value={[]}
          options={mockUsers}
          onChange={mockOnChange}
        />,
      );

      const mainInput = screen.getByRole('combobox');
      fireEvent.click(mainInput);

      expect(mainInput).toHaveAttribute('aria-expanded', 'true');
    });

    it('has proper checkbox roles and states', async () => {
      render(
        <SearchUserDropdown
          value={['1']}
          options={mockUsers}
          onChange={mockOnChange}
        />,
      );

      const mainInput = screen.getByRole('combobox');
      fireEvent.click(mainInput);

      await waitFor(() => {
        const checkbox = screen.getByTestId('checkbox-user-check-1');
        expect(checkbox).toBeChecked();
      });
    });
  });
});
