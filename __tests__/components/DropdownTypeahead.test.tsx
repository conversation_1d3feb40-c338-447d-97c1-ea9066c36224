import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import DropdownTypeahead, {
  Option,
} from '../../src/components/DropdownTypeahead';

describe('DropdownTypeahead', () => {
  const mockOptions: Option[] = [
    {value: '1', label: 'Option 1'},
    {value: '2', label: 'Option 2'},
    {value: '3', label: 'Option 3'},
  ];

  const defaultProps = {
    label: 'Test Dropdown',
    options: mockOptions,
    selected: null,
    onChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with proper accessibility attributes', () => {
    const {container} = render(<DropdownTypeahead {...defaultProps} />);

    const input = screen.getByRole('combobox');
    expect(input).toHaveAttribute('aria-autocomplete', 'both');

    const label = container.querySelector('label.fs-14.fw-500');
  });

  it('hides label when hideLabel prop is true', () => {
    const {container} = render(
      <DropdownTypeahead {...defaultProps} hideLabel={true} />,
    );
    const label = container.querySelector('.fs-14.fw-500');
    expect(label).toHaveStyle({display: 'none'});
  });

  it('disables the input when disabled prop is true', () => {
    render(<DropdownTypeahead {...defaultProps} disabled={true} />);

    const input = screen.getByRole('combobox');
    expect(input).toBeDisabled();
  });

  it('shows error message when isInvalid is true', () => {
    const errorMessage = 'Custom error message';
    render(
      <DropdownTypeahead
        {...defaultProps}
        isInvalid={true}
        errorMessage={errorMessage}
      />,
    );

    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  });

  it('handles single selection correctly', async () => {
    render(<DropdownTypeahead {...defaultProps} />);

    const input = screen.getByRole('combobox');
    fireEvent.focus(input);
    fireEvent.click(input);

    const option = await screen.findByText('Option 1');
    fireEvent.click(option);

    expect(defaultProps.onChange).toHaveBeenCalledWith({
      value: '1',
      label: 'Option 1',
    });
  });

  it('handles multiple selection correctly', async () => {
    const onChange = jest.fn();
    render(
      <DropdownTypeahead
        {...defaultProps}
        multiple={true}
        onChange={onChange}
        selected={[]}
      />,
    );

    const input = screen.getByRole('combobox');
    fireEvent.focus(input);
    fireEvent.click(input);

    const option1 = await screen.findByText('Option 1');
    fireEvent.click(option1);

    expect(onChange).toHaveBeenCalledWith([
      {
        value: '1',
        label: 'Option 1',
      },
    ]);
  });

  it('calls onInputChange when typing in the input', async () => {
    const onInputChange = jest.fn();
    render(
      <DropdownTypeahead {...defaultProps} onInputChange={onInputChange} />,
    );

    const input = screen.getByRole('combobox');
    await userEvent.type(input, 't');

    // Only check the first argument since the second is a React synthetic event
    expect(onInputChange.mock.calls[0][0]).toBe('t');
  });

  it('displays "No results found" when no options match search', async () => {
    render(<DropdownTypeahead {...defaultProps} />);

    const input = screen.getByRole('combobox');
    await userEvent.type(input, 'nonexistent');

    expect(screen.getByText('No results found')).toBeInTheDocument();
  });

  it('handles special option selection', async () => {
    const onSpecialOptionSelect = jest.fn();
    render(
      <DropdownTypeahead
        {...defaultProps}
        specialOptionLabel="Add New Option"
        onSpecialOptionSelect={onSpecialOptionSelect}
      />,
    );

    const input = screen.getByRole('combobox');
    fireEvent.focus(input);
    fireEvent.click(input);

    const specialOption = await screen.findByText('Add New Option');
    fireEvent.click(specialOption);

    expect(onSpecialOptionSelect).toHaveBeenCalled();
  });

  it('displays token with more count for multiple selection', () => {
    const selected = [
      {value: '1', label: 'Option 1'},
      {value: '2', label: 'Option 2'},
      {value: '3', label: 'Option 3'},
      {value: '4', label: 'Option 4'},
      {value: '5', label: 'Option 5'},
    ];

    render(
      <DropdownTypeahead
        {...defaultProps}
        multiple={true}
        selected={selected}
      />,
    );

    expect(
      screen.getByText('Option 1, Option 2, Option 3, Option 4'),
    ).toBeInTheDocument();
    const {container} = render(
      <DropdownTypeahead
        {...defaultProps}
        multiple={true}
        selected={selected}
      />,
    );
    const element = container.getElementsByClassName('rbt-token-more')[0];
    expect(element).toHaveTextContent('+1 More');
  });

  it('clears selection when clear button is clicked', async () => {
    const onChange = jest.fn();
    const selected = {value: '1', label: 'Option 1'};
    render(
      <DropdownTypeahead
        {...defaultProps}
        selected={selected}
        onChange={onChange}
      />,
    );

    const clearButton = screen
      .getByTestId('clear-icon')
      .querySelector('button');
    fireEvent.click(clearButton!);

    expect(onChange).toHaveBeenCalledWith(null);
  });

  it('toggles dropdown when clear icon is clicked with no selection', () => {
    render(<DropdownTypeahead {...defaultProps} />);

    const clearButton = screen
      .getByTestId('clear-icon')
      .querySelector('button');
    fireEvent.click(clearButton!);

    expect(screen.getByTestId('clear-icon')).toBeInTheDocument();
  });

  it('renders custom placeholder when disabledSelectPrefix is true', () => {
    render(<DropdownTypeahead {...defaultProps} disabledSelectPrefix={true} />);

    const input = screen.getByRole('combobox');
    expect(input).toHaveAttribute('placeholder', 'Test Dropdown');
  });

  it('shows required indicator when required prop is true', () => {
    render(<DropdownTypeahead {...defaultProps} required={true} />);

    const labelElement = screen.getByText('Test Dropdown*');
    expect(labelElement).toBeInTheDocument();
  });

  it('handles non-object options correctly', async () => {
    const stringOptions = ['Option 1', 'Option 2', 'Option 3'];
    render(
      <DropdownTypeahead {...defaultProps} options={stringOptions as any[]} />,
    );

    const input = screen.getByRole('combobox');
    fireEvent.focus(input);
    fireEvent.click(input);

    const option = await screen.findByText('Option 1');
    expect(option).toBeInTheDocument();
  });
});
