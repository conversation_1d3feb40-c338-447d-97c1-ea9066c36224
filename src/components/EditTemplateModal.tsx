import React, {useRef, useState, useEffect} from 'react';
import {Modal, Button, Col, Form, Row} from 'react-bootstrap';
import {cloneDeep, isEqual} from 'lodash';
import {TemplateForm} from '../types/template';
import {RiskForm} from '../types/risk';
import {RaCategoryStep} from '../pages/CreateRA/RaCategoryStep';
import {HazardCategoryStep} from '../pages/CreateRA/HazardCategoryStep';
import {AtRiskStep} from '../pages/CreateRA/AtRiskStep';
import {AddJobsStep} from '../pages/CreateRA/AddJobsStep';
import EditBasicDetailsComp from './EditBasicDetail';

type Props = {
  onClose: () => void;
  title: string;
  step: number;
  form: TemplateForm | RiskForm;
  setForm: (f: any) => void;
  jobId?: string;
  type?: 'template' | 'risk';
};

export const EditTemplateModal: React.FC<Props> = ({
  onClose,
  title,
  step,
  form,
  setForm,
  jobId,
  type = 'template',
}) => {
  const [jobIndex, setJobIndex] = useState(0);

  const raCategoryRef = useRef<any>(null);
  const hazardCategoryRef = useRef<any>(null);
  const atRiskRef = useRef<any>(null);
  const addJobsRef = useRef<any>(null);

  // Create a cloned form for editing
  const [clonedForm, setClonedForm] = useState<TemplateForm | RiskForm>(() => {
    const cloned = cloneDeep(form);

    // For step 5 (AddJobsStep), filter to keep only the job with matching jobId
    if (step === 5 && jobId) {
      const filteredJob = (cloned as TemplateForm).template_job.find(
        job => job.job_id === jobId,
      );
      (cloned as TemplateForm).template_job = filteredJob ? [filteredJob] : [];
    }

    return cloned;
  });
  const [isFormValid, setIsFormValid] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    if (jobId && (form as TemplateForm).template_job?.length > 0) {
      const index = (form as TemplateForm).template_job.findIndex(
        job => job.job_id === jobId,
      );
      if (index !== -1) {
        setJobIndex(index);
      }
    }
  }, [jobId, (form as TemplateForm).template_job]);

  // Check for changes whenever clonedForm updates
  useEffect(() => {
    let formHasChanges = false;

    if (
      step === 5 &&
      jobId &&
      (clonedForm as TemplateForm).template_job.length > 0
    ) {
      // For step 5, compare only the specific job
      const originalJob = (form as TemplateForm).template_job.find(
        job => job.job_id === jobId,
      );
      const editedJob = (clonedForm as TemplateForm).template_job[0];
      formHasChanges = !isEqual(originalJob, editedJob);
    } else {
      // For other steps, compare the entire form
      formHasChanges = !isEqual(form, clonedForm);
    }

    setHasChanges(formHasChanges);
  }, [form, clonedForm, step, jobId]);

  // Validation for step 1
  useEffect(() => {
    if (step === 1) {
      let isStep1Valid = false;

      if (type === 'risk') {
        const riskForm = clonedForm as RiskForm;
        isStep1Valid =
          riskForm.task_requiring_ra.trim() !== '' &&
          riskForm.task_duration.trim() !== '' &&
          !!riskForm.assessor &&
          riskForm.assessor !== 0 &&
          !!riskForm.vessel_ownership_id &&
          riskForm.vessel_ownership_id !== 0 &&
          !!riskForm.date_risk_assessment &&
          riskForm.date_risk_assessment.trim() !== '' &&
          !!riskForm.approval_required &&
          riskForm.approval_required.length > 0;
      } else {
        // Template form validation (existing logic)
        isStep1Valid =
          clonedForm.task_requiring_ra.trim() !== '' &&
          clonedForm.task_duration.trim() !== '';
      }
      setIsFormValid(isStep1Valid);
    }
  }, [clonedForm, step, type]);

  // Handle validation from child components
  const handleValidation = (valid: boolean) => {
    setIsFormValid(valid);
  };

  const handleSave = async () => {
    // Validate the current step before saving
    let isValid = false;
    if (step === 1) {
      if (type === 'risk') {
        const riskForm = clonedForm as RiskForm;
        isValid =
          riskForm.task_requiring_ra.trim() !== '' &&
          riskForm.task_duration.trim() !== '' &&
          !!riskForm.assessor &&
          riskForm.assessor !== 0 &&
          !!riskForm.vessel_ownership_id &&
          riskForm.vessel_ownership_id !== 0 &&
          !!riskForm.date_risk_assessment &&
          riskForm.date_risk_assessment.trim() !== '' &&
          !!riskForm.approval_required &&
          riskForm.approval_required.length > 0;
      } else {
        // Template form validation (existing logic)
        isValid =
          clonedForm.task_requiring_ra.trim() !== '' &&
          clonedForm.task_duration.trim() !== '';
      }
    } else if (step === 2 && raCategoryRef.current) {
      isValid = raCategoryRef.current.validate();
    } else if (step === 3 && hazardCategoryRef.current) {
      isValid = hazardCategoryRef.current.validate();
    } else if (step === 4 && atRiskRef.current) {
      isValid = atRiskRef.current.validate();
    } else if (step === 5 && addJobsRef.current) {
      isValid = addJobsRef.current.validate();
    }

    if (isValid) {
      if (
        step === 5 &&
        jobId &&
        (clonedForm as TemplateForm).template_job.length > 0
      ) {
        // For step 5, merge back only the specific job that was edited
        const editedJob = (clonedForm as TemplateForm).template_job[0]; // The filtered job
        const updatedForm = cloneDeep(form);
        const jobIndex = (updatedForm as TemplateForm).template_job.findIndex(
          job => job.job_id === jobId,
        );

        if (jobIndex !== -1) {
          (updatedForm as TemplateForm).template_job[jobIndex] = editedJob;
        }

        setForm(updatedForm);
      } else {
        // For other steps, update the entire form
        setForm(cloneDeep(clonedForm));
      }
      onClose();
    }
  };
  const StepConfig: Record<number, {component: React.ReactNode}> = {
    1: {
      component: (
        <EditBasicDetailsComp
          clonedForm={clonedForm}
          setClonedForm={setClonedForm}
          onValidate={handleValidation}
          type={type}
        />
      ),
    },
    2: {
      component: (
        <RaCategoryStep
          ref={raCategoryRef}
          form={clonedForm}
          setForm={setClonedForm}
          onValidate={handleValidation}
          isEdit={true}
          type={type}
        />
      ),
    },
    3: {
      component: (
        <HazardCategoryStep
          ref={hazardCategoryRef}
          form={clonedForm}
          setForm={setClonedForm}
          onValidate={handleValidation}
          isEdit={true}
          type={type}
        />
      ),
    },
    4: {
      component: (
        <AtRiskStep
          ref={atRiskRef}
          form={clonedForm}
          setForm={setClonedForm}
          onValidate={handleValidation}
          isEdit={true}
        />
      ),
    },
    5: {
      component: (
        <AddJobsStep
          ref={addJobsRef}
          form={clonedForm}
          setForm={setClonedForm}
          onValidate={handleValidation}
          isEdit={true}
          jobIndex={jobIndex}
          type={type}
        />
      ),
    },
  };

  return (
    <Modal
      show
      onHide={onClose}
      size={step === 1 ? 'lg' : 'xl'}
      backdrop="static"
      dialogClassName="top-modal"
    >
      <Modal.Header>
        <Modal.Title>{title}</Modal.Title>
      </Modal.Header>
      <Modal.Body className="edit-modal-body">
        {StepConfig[step].component}
      </Modal.Body>
      <Modal.Footer>
        <Button
          variant="primary"
          className="me-2 fs-14"
          onClick={() => onClose()}
        >
          Cancel
        </Button>
        <Button
          variant="secondary"
          className="me-2 fs-14"
          onClick={handleSave}
          disabled={!isFormValid || !hasChanges}
        >
          Save Changes
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
