import React, {useRef} from 'react';
import {Typeahead, MenuItem} from 'react-bootstrap-typeahead';
import {Form, OverlayTrigger, Tooltip} from 'react-bootstrap';
import {
  ChevronDown,
  ChevronUp,
  X,
  ExclamationCircle,
} from 'react-bootstrap-icons';

export type Option = string | Record<string, any>;
export type DropdownTypeaheadProps = {
  label: string;
  options: Option[];
  selected: Option | Option[] | null | undefined;
  onChange: (value: Option | Option[] | null) => void;
  onInputChange?: (query: string) => void;
  onBlur?: () => void;
  multiple?: boolean;
  required?: boolean;
  disabled?: boolean;
  isInvalid?: boolean;
  errorMessage?: string;
  hideLabel?: boolean;
  disabledSelectPrefix?: boolean;
  specialOptionLabel?: string;
  onSpecialOptionSelect?: () => void;
};

const DropdownTypeahead: React.FC<DropdownTypeaheadProps> = ({
  label,
  options,
  selected,
  onChange,
  onInputChange,
  onBlur,
  hideLabel = false,
  required = false,
  disabled = false,
  isInvalid = false,
  errorMessage = 'This field is required',
  multiple = false,
  disabledSelectPrefix = false,
  specialOptionLabel,
  onSpecialOptionSelect,
}) => {
  const [isMenuOpen, setIsMenuOpen] = React.useState(false);
  const [userInteracted, setUserInteracted] = React.useState(false);
  const typeaheadRef = useRef<any>(null);
  const hasSelectedValue = () => {
    if (!selected) return false;
    return Array.isArray(selected) ? selected.length > 0 : true;
  };

  let selectedValue: Option[] = [];

  if (selected) {
    if (Array.isArray(selected)) {
      selectedValue = selected;
    } else {
      selectedValue = [selected];
    }
  }

  const renderActionIcon = () => {
    if (isInvalid && !hasSelectedValue() && multiple) {
      return (
        <ExclamationCircle
          data-testid="danger-icon"
          size={14}
          color="#dc3545"
        />
      );
    }
    if (isInvalid) return;
    if (hasSelectedValue()) {
      return <X data-testid="cross-icon" size={18} />;
    }
    return isMenuOpen ? <ChevronUp size={14} /> : <ChevronDown size={14} />;
  };

  return (
    <Form.Group>
      <Form.Label style={{display: hideLabel ? 'none' : 'block'}}>
        {label}
        {required && '*'}
      </Form.Label>
      <div
        className={`typeahead-wrapper ${isInvalid ? 'is-invalid' : ''} ${
          multiple ? 'multiple-selection' : ''
        }`}
        style={{
          position: 'relative',
          ...(isInvalid && {
            border: '1px solid #dc3545',
            borderRadius: '4px',
            ...(multiple && {
              backgroundColor: '#fff',
            }),
          }),
        }}
        onClick={() => {
          setUserInteracted(true);
        }}
      >
        <Typeahead
          ref={typeaheadRef}
          id={`${label.replace(/\s+/g, '-').toLowerCase()}-typeahead`}
          labelKey="label"
          maxResults={250}
          options={options}
          onMenuToggle={isOpen => {
            setIsMenuOpen(isOpen);
            if (!isOpen && userInteracted && onBlur) {
              onBlur();
              setUserInteracted(false);
            }
          }}
          placeholder={disabledSelectPrefix ? label : `Select ${label}`}
          onChange={(selectedArr: Option[]) => {
            if (multiple) {
              onChange(selectedArr.length > 0 ? selectedArr : null);
            } else {
              onChange(selectedArr[0] || null);
            }
          }}
          onInputChange={onInputChange}
          selected={selectedValue}
          minLength={0}
          disabled={disabled}
          multiple={multiple}
          inputProps={{
            className: `fs-14 ${isInvalid ? 'is-invalid' : ''}`,
            style: {
              paddingRight: '30px',
              ...(isInvalid && {
                borderColor: '#dc3545',
                boxShadow: '0 0 0 0.2rem rgba(220, 53, 69, 0.25)',
              }),
            },
            onFocus: () => {
              setUserInteracted(true);
            },
            onKeyDown: () => {
              setUserInteracted(true);
            },
          }}
          renderMenu={(optionList: Option[], menuProps) => {
            const getOptionKey = (option: Option, index: number) => {
              if (typeof option === 'object') {
                return option.id ?? option.value ?? option.label ?? index;
              }
              return option;
            };

            const getOptionLabel = (option: Option) => {
              return typeof option === 'object' && 'label' in option
                ? option.label
                : option;
            };

            const renderOptions = () =>
              optionList.map((option: Option, i: number) => (
                <MenuItem
                  key={getOptionKey(option, i)}
                  option={option}
                  position={i}
                >
                  <div
                    className="typehead-option-tooltip"
                    title={getOptionLabel(option)}
                  >
                    {getOptionLabel(option)}
                  </div>
                </MenuItem>
              ));

            const renderSpecialOption = () =>
              specialOptionLabel && onSpecialOptionSelect ? (
                <button
                  type="button"
                  className="dropdown-special-item"
                  onClick={onSpecialOptionSelect}
                >
                  {specialOptionLabel}
                </button>
              ) : null;

            return (
              <div
                className="rbt-menu dropdown-menu show nbp-typehead-dropdown"
                {...menuProps}
              >
                {optionList.length === 0 ? (
                  <div className="dropdown-no-results">No results found</div>
                ) : (
                  renderOptions()
                )}
                {renderSpecialOption()}
              </div>
            );
          }}
          renderToken={(option, props, index) => {
            const visibleCount = 4;

            // Only render on the first token
            if (index !== 0) return <></>;

            const selectedValues = Array.isArray(selected)
              ? selected
              : [selected];

            const visibleItems = selectedValues.slice(0, visibleCount);
            const hiddenItems = selectedValues.slice(visibleCount);

            const visibleLabels = visibleItems.map(opt => opt.label).join(', ');
            const hiddenLabels = hiddenItems.map(opt => opt.label).join(', ');

            return (
              <div className="rbt-token">
                <span>{visibleLabels}</span>
                {hiddenItems.length > 0 && (
                  <OverlayTrigger
                    placement="top"
                    overlay={
                      <Tooltip id="info-tooltip">{hiddenLabels}</Tooltip>
                    }
                  >
                    <span className="rbt-token-more">
                      +{hiddenItems.length} More
                    </span>
                  </OverlayTrigger>
                )}
              </div>
            );
          }}
        />
        {!disabled && (
          <div
            className="clear-icon"
            data-testid="clear-icon"
            style={{
              position: 'absolute',
              right: '10px',
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 4,
            }}
          >
            <button
              type="button"
              style={{
                all: 'unset', // Removes all default styles
              }}
              className="cursor-pointer"
              onClick={() => {
                if (hasSelectedValue()) {
                  onChange(null);
                } else if (!isInvalid) {
                  typeaheadRef.current?.toggleMenu?.(); // Opens the dropdown
                }
              }}
            >
              {renderActionIcon()}
            </button>
          </div>
        )}
      </div>
      {isInvalid && (
        <div className="invalid-feedback" style={{display: 'block'}}>
          {errorMessage}
        </div>
      )}
    </Form.Group>
  );
};

export default DropdownTypeahead;
