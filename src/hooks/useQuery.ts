import {useState, useEffect, useCallback, useRef} from 'react';

export type QueryStatus = 'idle' | 'loading' | 'success' | 'error';

export type QueryOptions<TData, TError> = {
  enabled?: boolean;
  retry?: number | boolean;
  retryDelay?: number;
  staleTime?: number;
  cacheTime?: number;
  onSuccess?: (data: TData) => void;
  onError?: (error: TError) => void;
  onSettled?: (data: TData | undefined, error: TError | null) => void;
};

export type QueryResult<TData, TError> = {
  data: TData | undefined;
  error: TError | null;
  isLoading: boolean;
  isError: boolean;
  isSuccess: boolean;
  isIdle: boolean;
  status: QueryStatus;
  refetch: () => Promise<TData>;
  isFetching: boolean;
};

// Cache implementation for storing query results
const queryCache = new Map<
  string,
  {
    data: any;
    lastUpdated: number;
  }
>();

export function useQuery<TData = unknown, TError = Error>(
  queryKey: string | string[],
  queryFn: () => Promise<TData>,
  options: QueryOptions<TData, TError> = {},
): QueryResult<TData, TError> {
  // Convert queryKey to a string for cache key
  const queryKeyString = Array.isArray(queryKey)
    ? queryKey.join('-')
    : queryKey;

  // Destructure options with defaults
  const {
    enabled = true,
    retry = 3,
    retryDelay = 1000,
    staleTime = 0,
    cacheTime = 5 * 60 * 1000, // 5 minutes
    onSuccess,
    onError,
    onSettled,
  } = options;

  // Set up state
  const [status, setStatus] = useState<QueryStatus>('idle');
  const [data, setData] = useState<TData | undefined>(undefined);
  const [error, setError] = useState<TError | null>(null);
  const [isFetching, setIsFetching] = useState<boolean>(false);

  // Keep track of retry count
  const retryCount = useRef(0);

  // Check if there's cached data available
  useEffect(() => {
    const cachedEntry = queryCache.get(queryKeyString);

    if (cachedEntry) {
      const isStale = Date.now() - cachedEntry.lastUpdated > staleTime;

      // Use cached data
      setData(cachedEntry.data);
      setStatus('success');

      // If data is stale, refetch in background
      if (isStale && enabled) {
        fetchData();
      }
    } else if (enabled) {
      // No cache, fetch data if enabled
      fetchData();
    }
  }, [queryKeyString, enabled]);

  // Cleanup function to remove from cache after cacheTime
  useEffect(() => {
    const cleanup = setTimeout(() => {
      queryCache.delete(queryKeyString);
    }, cacheTime);

    return () => clearTimeout(cleanup);
  }, [queryKeyString, cacheTime]);

  // Main fetch function
  const fetchData = useCallback(async (): Promise<TData> => {
    setIsFetching(true);
    if (status === 'idle') {
      setStatus('loading');
    }

    try {
      const result = await queryFn();

      // Update cache
      queryCache.set(queryKeyString, {
        data: result,
        lastUpdated: Date.now(),
      });

      // Update state
      setData(result);
      setStatus('success');
      setError(null);
      retryCount.current = 0;

      // Call onSuccess callback if provided
      onSuccess?.(result);
      onSettled?.(result, null);

      return result;
    } catch (err) {
      setStatus('error');
      setError(err as TError);

      // Handle retry logic
      const shouldRetry =
        typeof retry === 'boolean' ? retry : retryCount.current < retry;

      if (shouldRetry) {
        retryCount.current++;
        const retryTimer = setTimeout(() => {
          fetchData();
        }, retryDelay);

        return new Promise((resolve, reject) => {
          // Clean up timer on component unmount
          const unsubscribe = () => {
            clearTimeout(retryTimer);
            reject(err);
          };
        });
      }

      // Call callbacks
      onError?.(err as TError);
      onSettled?.(undefined, err as TError);

      throw err;
    } finally {
      setIsFetching(false);
    }
  }, [queryFn, queryKeyString]);

  // Refetch function exposed in the return value
  const refetch = useCallback(() => {
    return fetchData();
  }, [fetchData]);

  return {
    data,
    error,
    isLoading: status === 'loading',
    isError: status === 'error',
    isSuccess: status === 'success',
    isIdle: status === 'idle',
    status,
    refetch,
    isFetching,
  };
}
