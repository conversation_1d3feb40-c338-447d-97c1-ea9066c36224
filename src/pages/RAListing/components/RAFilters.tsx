import React, {useEffect, useMemo, useState} from 'react';
import {toast} from 'react-toastify';
import {Row, Col, Form} from 'react-bootstrap';
import SearchInput from '../../../components/SearchInput';
import DropdownTypeahead from '../../../components/DropdownTypeahead';
import {
  getOfficesList,
  getRAStringOptions,
  getVesselsList,
} from '../../../services/services';
import CustomDatePickerWithRange from '../../../components/CustomDatePickerWithRange';
import {RaLevel, RAStatus} from '../../../enums';
import RAMoreFiltersDrawer from './RAMoreFiltersDrawer';
import VesselAndOfficeDropdown from '../../../components/VesselAndOfficeDropdown';
import SearchDropdown from '../../../components/SearchDropdown';
import {groupBy} from 'lodash';
import {vesselStatusAndLabelName} from '../../../utils/common';

import '../../../styles/components/ra-filters.scss';

const statuses = [
  {value: RAStatus.APPROVED, label: 'Approved'},
  // {value: RAStatus.DRAFT, label: 'Draft'}, - Draft status is not used in the filter
  {value: RAStatus.INACTIVE, label: 'Inactive'},
  {value: RAStatus.PUBLISHED, label: 'Published'},
  {value: RAStatus.REJECTED, label: 'Rejected'},
];
const raLevels = [
  {label: 'Critical', value: RaLevel.CRITICAL},
  {label: 'Level 1 RA', value: RaLevel.LEVEL_1_RA},
  {label: 'Routine', value: RaLevel.ROUTINE},
  {label: 'Special', value: RaLevel.SPECIAL},
];

export type FilterOption = {
  label: string;
  value: string | number;
  full_name?: string;
  designation?: string;
  status?: string;
};

export type DateRangeValue = [string | null, string | null] | null;

export type FilterValue =
  | string
  | null
  | FilterOption
  | number[]
  | (string | number)[]
  | DateRangeValue
  | {
      vessel_id: number[] | null;
      office_id: number[] | null;
    };

export interface RAFilterValues {
  search: string | null;
  approval_status: (string | number)[];
  vessel_or_office: {
    vessel_id: number[] | null;
    office_id: number[] | null;
  } | null;
  vessel_category: number[];
  ra_level: (string | number)[];
  submitted_on: DateRangeValue;
  assessment_date: DateRangeValue;
  approval_date: DateRangeValue;
}

export const raFiltersInitialState: RAFilterValues = {
  search: null,
  approval_status: [],
  vessel_or_office: null,
  vessel_category: [],
  ra_level: [],
  submitted_on: null,
  approval_date: null,
  assessment_date: null,
};

export interface RAFiltersProps {
  filters: RAFilterValues;
  onFilterChange: (key: keyof RAFilterValues, value: FilterValue) => void;
}

export const RAFilters: React.FC<RAFiltersProps> = ({
  filters,
  onFilterChange,
}) => {
  const [vessels, setVessels] = useState<FilterOption[]>([]);
  const [offices, setOffices] = useState<FilterOption[]>([]);
  const [vesselCategories, setVesselCategories] = useState<FilterOption[]>([]);

  useEffect(() => {
    const loadBasicDetails = async () => {
      try {
        const [raVesselCategory, raVessel, raOffice] = await Promise.all([
          getRAStringOptions('vessel_category'),
          getVesselsList(),
          getOfficesList(),
        ]);
        setVessels(
          raVessel.map(item => ({
            label: item.name,
            value: item.vessel.id,
            status: item.status,
          })),
        );
        setOffices(
          raOffice.map(item => ({
            label: item.value,
            value: item.id,
          })),
        );
        setVesselCategories(
          raVesselCategory.result.map(item => ({
            label: item,
            value: item,
          })),
        );
      } catch (error) {
        console.error('Error loading data:', error);
        toast.error('Failed to load data. Please try again later.');
      }
    };
    loadBasicDetails();
  }, []);

  const filterConfig = useMemo(
    () =>
      getRaBasicFiltersFormConfig(
        {filters, onFilterChange},
        {vessels, vesselCategories, offices},
      ),
    [filters, onFilterChange, vessels, vesselCategories, offices],
  );

  return (
    <div className="ra-filters">
      <Row className="equal-width-cols">
        {filterConfig.map(({key, component}) => (
          <Col key={key} className="col-equal">
            <Form.Group controlId={key}>{component}</Form.Group>
          </Col>
        ))}
        <Col className="col-equal">
          <Form.Group>
            <RAMoreFiltersDrawer
              onFilterChange={onFilterChange}
              optionsData={{
                vessels,
                vesselCategories,
                offices,
              }}
            />
          </Form.Group>
        </Col>
      </Row>
    </div>
  );
};

export const getRaBasicFiltersFormConfig = (
  raFilterProps: RAFiltersProps,
  optionsData: {
    vessels: FilterOption[];
    vesselCategories: FilterOption[];
    offices: FilterOption[];
  },
) => {
  const {filters, onFilterChange} = raFilterProps;
  const {vessels, vesselCategories, offices} = optionsData;
  return [
    {
      key: 'search' as const,
      label: 'Search by Task Name',
      component: (
        <SearchInput
          value={filters.search || ''}
          onSearch={value => onFilterChange('search', value || null)}
          placeholder="Search by Task Name"
        />
      ),
    },
    {
      key: 'approval_status' as const,
      label: 'Approval Status',
      component: (
        <SearchDropdown
          placeholder="Approval Status"
          options={statuses}
          selected={filters.approval_status}
          onChange={value => onFilterChange('approval_status', value)}
          multiple={false}
        />
      ),
    },
    {
      key: 'vessel_id' as const,
      label: 'Vessel/Office Name',
      component: (
        <VesselAndOfficeDropdown
          value={filters.vessel_or_office}
          options={[
            ...Object.entries(groupBy(vessels, 'status'))
              .sort((a, b) => a[0].localeCompare(b[0]))
              .map(([status, vessels]) => ({
                label: vesselStatusAndLabelName[String(status)] || status,
                data: vessels.map(vessel => ({
                  id: vessel.value,
                  name: vessel.label,
                })),
              })),
            {
              label: 'Offices',
              data: offices.map((office: FilterOption) => ({
                id: office.value,
                name: office.label,
              })),
            },
          ]}
          placeholder="Select Vessel/Office"
          onChange={value => onFilterChange('vessel_or_office', value)}
        />
      ),
    },
    {
      key: 'vessel_category' as const,
      label: 'Vessel Category',
      component: (
        <SearchDropdown
          placeholder="Vessel Category"
          options={vesselCategories}
          selected={filters.vessel_category}
          onChange={value => {
            onFilterChange('vessel_category', value);
          }}
        />
      ),
    },
    {
      key: 'ra_level' as const,
      label: 'Level of RA',
      component: (
        <SearchDropdown
          placeholder="Level of RA"
          options={raLevels}
          selected={filters.ra_level}
          onChange={value => onFilterChange('ra_level', value)}
          multiple={false}
        />
      ),
    },
    {
      key: 'submission_date' as const,
      label: 'Submitted on',
      component: (
        <CustomDatePickerWithRange
          controlId="ra_filters_submitted_on"
          placeholder="Submitted on"
          startDate={
            filters.submitted_on?.[0]
              ? new Date(filters.submitted_on?.[0])
              : undefined
          }
          endDate={
            filters.submitted_on?.[1]
              ? new Date(filters.submitted_on?.[1])
              : undefined
          }
          onChange={([start, end]) =>
            onFilterChange('submitted_on', [start ?? null, end ?? null])
          }
        />
      ),
    },
  ];
};
